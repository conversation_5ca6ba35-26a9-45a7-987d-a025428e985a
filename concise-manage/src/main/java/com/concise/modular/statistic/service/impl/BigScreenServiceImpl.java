package com.concise.modular.statistic.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.concise.common.consts.CommonConstant;
import com.concise.common.factory.PageFactory;
import com.concise.common.file.service.SysFileInfoService;
import com.concise.common.pojo.page.PageResult;
import com.concise.core.context.login.LoginContextHolder;
import com.concise.core.login.SysLoginUser;
import com.concise.modular.benchmark.benchmarkmanage.entity.BenchmarkManage;
import com.concise.modular.benchmark.benchmarkmanage.service.BenchmarkManageService;
import com.concise.modular.evaluation.cityareainfo.entity.CityAreaInfo;
import com.concise.modular.evaluation.cityareainfo.mapper.CityAreaInfoMapper;
import com.concise.modular.evaluation.cityareainfo.service.CityAreaInfoService;
import com.concise.modular.evaluation.examresult.entity.ExamResult;
import com.concise.modular.law.lawposition.entity.LawPosition;
import com.concise.modular.law.lawposition.enums.LawPositionBaseEnum;
import com.concise.modular.law.lawposition.service.LawPositionService;
import com.concise.modular.laweducation.entity.LawEducationResourcesView;
import com.concise.modular.laweducation.entity.LeResources;
import com.concise.modular.laweducation.entity.LerLabel;
import com.concise.modular.laweducation.mapper.LerLabelMapper;
import com.concise.modular.laweducation.param.LeResourcesParam;
import com.concise.modular.laweducation.param.LerLabelParam;
import com.concise.modular.laweducation.service.LawEducationResourcesViewService;
import com.concise.modular.laweducation.service.LeResourcesService;
import com.concise.modular.laweducation.service.LerLabelService;
import com.concise.modular.obpoint.entity.ObPointInfo;
import com.concise.modular.obpoint.service.ObPointInfoService;
import com.concise.modular.statistic.enums.FiveLawEnum;
import com.concise.modular.statistic.enums.LawLearnerEducationEnum;
import com.concise.modular.statistic.enums.LawLearnerPersonTypeEnum;
import com.concise.modular.statistic.enums.LawLearnerPoliticalEnum;
import com.concise.modular.statistic.enums.LawPositionTypeEnum;
import com.concise.modular.statistic.enums.ResourceTypeDetailEnum;
import com.concise.modular.statistic.enums.ResourceTypeEnum;
import com.concise.modular.statistic.enums.ThreeDimensionalEnum;
import com.concise.modular.statistic.mapper.BigScreenMapper;
import com.concise.modular.statistic.service.BigScreenService;
import com.concise.modular.statistic.vo.LawPositionVo;
import com.concise.modular.statistic.vo.LawUserVo;
import com.concise.modular.statistic.vo.MetricsVo;
import com.concise.modular.statistic.vo.ResourceVo;
import com.concise.modular.statistic.vo.ScreenVo;
import com.concise.sys.modular.auth.service.AuthService;
import com.concise.sys.modular.dict.entity.SysDictData;
import com.concise.sys.modular.dict.entity.SysDictType;
import com.concise.sys.modular.dict.service.SysDictDataService;
import com.concise.sys.modular.dict.service.SysDictTypeService;
import com.concise.sys.modular.emp.service.SysEmpService;
import com.concise.sys.modular.log.entity.SysVisLog;
import com.concise.sys.modular.log.service.SysVisLogService;
import com.concise.sys.modular.org.entity.SysOrg;
import com.concise.sys.modular.org.service.SysOrgService;
import com.concise.sys.modular.user.entity.SysUser;
import com.concise.sys.modular.user.service.SysUserService;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/9/25
 * 驾驶舱service实现类
 */
@Slf4j
@Service
public class BigScreenServiceImpl implements BigScreenService {

    @Resource
    private LawPositionService lawPositionService;

    @Resource
    private SysUserService sysUserService;

    @Resource
    private BigScreenMapper bigScreenMapper;

    @Resource
    private LeResourcesService leResourcesService;

    @Resource
    private SysOrgService sysOrgService;

    @Resource
    private ObPointInfoService obPointInfoService;

    @Resource
    private CityAreaInfoMapper cityAreaInfoMapper;

    @Resource
    private CityAreaInfoService cityAreaInfoService;

    @Resource
    private AuthService authService;

    @Resource
    private SysVisLogService sysVisLogService;

    @Resource
    private LawEducationResourcesViewService lawEducationResourcesViewService;

    @Resource
    private LerLabelService lerLabelService;

    @Resource
    private LerLabelMapper lerLabelMapper;

    @Resource
    private BenchmarkManageService benchmarkManageService;

    @Resource
    private SysDictTypeService sysDictTypeService;

    @Resource
    private SysDictDataService sysDictDataService;

    @Resource
    private SysEmpService sysEmpService;

    @Resource
    private SysFileInfoService sysFileInfoService;



    /**
     * 根据区域名称获取区域编码
     *
     * @param areaName
     * @return
     */
    private String getAreaCodeByName(String areaName) {
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            return CommonConstant.TOP_AREA_CODE;
        }
//        CityEnum enumByName = CityEnum.getEnumByName(areaName);
//        if (enumByName != null) {
//            return enumByName.getCode();
//        }
        return cityAreaInfoMapper.getIdByName(areaName);

    }

    /**
     * 根据区域名称获取sys_org
     *
     * @param areaName
     * @return
     */
    private String getOrgIdByName(String areaName) {
        String areaId = cityAreaInfoMapper.getIdByName(areaName);
        if (areaId != null) {
            return sysOrgService.getIdByAreaId(areaId);
        }
        return null;
    }

    @Override
    public List<ScreenVo> lawPosition(String areaName) {
        String areaCode = getAreaCodeByName(areaName);
        List<ScreenVo> screenVos = lawPositionService.lawPositionTypeCount(areaCode);
        List<ScreenVo> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(screenVos)) {
            for (LawPositionTypeEnum lawPositionTypeEnum : LawPositionTypeEnum.values()) {
                if (Objects.equals(lawPositionTypeEnum.getCode(), 5)) {
                    int count = obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().like(ObPointInfo::getAddressId, areaCode).eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4));
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setCode(5);
                    screenVo.setName(lawPositionTypeEnum.getName());
                    screenVo.setValue(count);
                    list.add(screenVo);
                } else {
                    Optional<ScreenVo> first = screenVos.stream().filter(screenVo -> Objects.equals(lawPositionTypeEnum.getCode(), screenVo.getCode())).findFirst();
                    if (first.isPresent()) {
                        ScreenVo screenVo = first.get();
                        screenVo.setName(lawPositionTypeEnum.getName());
                        list.add(screenVo);
                    } else {
                        list.add(new ScreenVo(lawPositionTypeEnum.getCode(), lawPositionTypeEnum.getName(), 0, null));

                    }
                }

            }
        } else {
            for (LawPositionTypeEnum lawPositionTypeEnum : LawPositionTypeEnum.values()) {
                if (Objects.equals(lawPositionTypeEnum.getCode(), 5)) {
                    int count = obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4));
                    ScreenVo screenVo = new ScreenVo();
                    screenVo.setCode(5);
                    screenVo.setName(lawPositionTypeEnum.getName());
                    screenVo.setValue(count);
                    list.add(screenVo);
                } else {
                    list.add(new ScreenVo(lawPositionTypeEnum.getCode(), lawPositionTypeEnum.getName(), 0, null));
                }
            }
        }
        return list;
    }

    @Override
    public Map<String, Object> lawCultivate(String areaName) {
        Map<String, Object> map = new HashMap<>();
        String areaCode = getAreaCodeByName(areaName);
        //省级
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            //男女比例
            List<ScreenVo> femaleAndMale = bigScreenMapper.lawCultivateSex(null);
            int total = 0;
            for (ScreenVo screenVo : femaleAndMale) {
                if (screenVo.getCode() == 1) {
                    screenVo.setName("男");
                }
                if (screenVo.getCode() == 2) {
                    screenVo.setName("女");
                }
                total += screenVo.getValue();
            }
            map.put("femaleAndMale", femaleAndMale);
            //总人数
            map.put("total", total);
            //省市区分布
            List<ScreenVo> provinceAndCity = new ArrayList<>();
            ScreenVo leveOne = bigScreenMapper.lawCultivateArea(1);
            leveOne.setName("省级普法干部");
            provinceAndCity.add(leveOne);
            ScreenVo leveTwo = bigScreenMapper.lawCultivateArea(2);
            leveTwo.setName("地市普法干部");
            provinceAndCity.add(leveTwo);
            ScreenVo leveThree = bigScreenMapper.lawCultivateArea(3);
            leveThree.setName("区县普法干部");
            provinceAndCity.add(leveThree);
            map.put("provinceAndCity", provinceAndCity);

            return map;
        } else {
            //地市区县
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                //男女比例
                List<ScreenVo> femaleAndMale = bigScreenMapper.lawCultivateSex(areaCode);
                int total = 0;
                for (ScreenVo screenVo : femaleAndMale) {
                    if (screenVo.getCode() == 1) {
                        screenVo.setName("男");
                    }
                    if (screenVo.getCode() == 2) {
                        screenVo.setName("女");
                    }
                    total += screenVo.getValue();
                }
                //如果男女都没有，补充0
                if (femaleAndMale.stream().noneMatch(e -> e.getCode() == 1) && femaleAndMale.stream().noneMatch(e -> e.getCode() == 2)) {
                    femaleAndMale.add(new ScreenVo(1, "男", 0, null));
                    femaleAndMale.add(new ScreenVo(2, "女", 0, null));
                }
                //如果男的没有，补充0
                if (femaleAndMale.stream().noneMatch(e -> e.getCode() == 1)) {
                    femaleAndMale.add(new ScreenVo(1, "男", 0, null));
                }
                //如果女的没有，补充0
                if (femaleAndMale.stream().noneMatch(e -> e.getCode() == 2)) {
                    femaleAndMale.add(new ScreenVo(2, "女", 0, null));
                }
                //排序，先男后女
                femaleAndMale.sort(Comparator.comparingInt(ScreenVo::getCode));
                map.put("femaleAndMale", femaleAndMale);
                //总人数
                map.put("total", total);

                //省市区分布情况
                SysOrg sysOrg = sysOrgService.getById(orgId);
                Integer level = sysOrg.getLevel();
                //省市区分布
                List<ScreenVo> provinceAndCity = new ArrayList<>();
                //查询地市，省级默认为0
                ScreenVo leveOne = new ScreenVo();
                leveOne.setName("省级普法干部");
                leveOne.setValue(0);
                provinceAndCity.add(leveOne);
                //只有地市的时候才有数据，地市的时候尝试往上找一级，如果还是同一层级，取上级
                if (level == 2) {
                    ScreenVo leveTwo = bigScreenMapper.lawCultivateAreaByOrgId(2, orgId);
                    leveTwo.setName("地市普法干部");
                    provinceAndCity.add(leveTwo);
                    //如果还是同一层级，取上级
                    SysOrg sysOrg1 = sysOrgService.getById(sysOrg.getPid());
                    if (Objects.equals(sysOrg1.getLevel(), level)) {
                        ScreenVo leveThree = bigScreenMapper.lawCultivateAreaByOrgId(3, sysOrg1.getId());
                        leveThree.setName("区县普法干部");
                        provinceAndCity.add(leveThree);
                    } else {
                        ScreenVo leveThree = bigScreenMapper.lawCultivateAreaByOrgId(3, sysOrg.getId());
                        leveThree.setName("区县普法干部");
                        provinceAndCity.add(leveThree);
                    }
                } else {
                    ScreenVo leveTwo = new ScreenVo();
                    leveTwo.setName("地市普法干部");
                    leveTwo.setValue(0);
                    provinceAndCity.add(leveTwo);
                    //区县一直有数据
                    ScreenVo leveThree = bigScreenMapper.lawCultivateAreaByOrgId(3, orgId);
                    leveThree.setName("区县普法干部");
                    provinceAndCity.add(leveThree);
                }

                map.put("provinceAndCity", provinceAndCity);
            } else {
                //没有数据，补充0
                List<ScreenVo> femaleAndMale = new ArrayList<>();
                femaleAndMale.add(new ScreenVo(1, "男", 0, null));
                femaleAndMale.add(new ScreenVo(2, "女", 0, null));
                map.put("femaleAndMale", femaleAndMale);
                map.put("total", 0);
                List<ScreenVo> provinceAndCity = new ArrayList<>();
                ScreenVo leveOne = new ScreenVo();
                leveOne.setName("省级普法干部");
                leveOne.setValue(0);
                provinceAndCity.add(leveOne);
                ScreenVo leveTwo = new ScreenVo();
                leveTwo.setName("地市普法干部");
                provinceAndCity.add(leveTwo);
                leveTwo.setValue(0);
                ScreenVo leveThree = new ScreenVo();
                leveThree.setName("区县普法干部");
                leveThree.setValue(0);
                provinceAndCity.add(leveThree);
                map.put("provinceAndCity", provinceAndCity);
            }
            return map;
        }

    }

    @Override
    public Map<String, Object> lawLearner(String areaName) {
        Map<String, Object> map = new HashMap<>();
        String areaCode = getAreaCodeByName(areaName);
        //政治面貌
        List<ScreenVo> political = bigScreenMapper.lawLearnerPolitical(areaCode);
        if (CollectionUtil.isNotEmpty(political)) {
            for (LawLearnerPoliticalEnum lawLearnerPoliticalEnum : LawLearnerPoliticalEnum.values()) {
                Optional<ScreenVo> first = political.stream().filter(e -> lawLearnerPoliticalEnum.getDict().equals(e.getName())).findFirst();
                if (first.isPresent()) {
                    ScreenVo screenVo = first.get();
                    screenVo.setCode(lawLearnerPoliticalEnum.getCode());
                    screenVo.setName(lawLearnerPoliticalEnum.getName());
                } else {
                    political.add(new ScreenVo(lawLearnerPoliticalEnum.getCode(), lawLearnerPoliticalEnum.getName(), 0, null));
                }
            }
        } else {
            political = new ArrayList<>();
            for (LawLearnerPoliticalEnum lawLearnerPoliticalEnum : LawLearnerPoliticalEnum.values()) {
                political.add(new ScreenVo(lawLearnerPoliticalEnum.getCode(), lawLearnerPoliticalEnum.getName(), 0, null));
            }
        }
        map.put("political", political);
        //学历
        List<ScreenVo> education = bigScreenMapper.lawLearnerEducation(areaCode);
        if (CollectionUtil.isNotEmpty(education)) {
            for (LawLearnerEducationEnum lawLearnerEducationEnum : LawLearnerEducationEnum.values()) {
                Optional<ScreenVo> first = education.stream().filter(e -> lawLearnerEducationEnum.getCode().equals(e.getCode())).findFirst();
                if (first.isPresent()) {
                    ScreenVo screenVo = first.get();
                    screenVo.setName(lawLearnerEducationEnum.getName());
                } else {
                    education.add(new ScreenVo(lawLearnerEducationEnum.getCode(), lawLearnerEducationEnum.getName(), 0, null));
                }
            }
        } else {
            education = new ArrayList<>();
            for (LawLearnerEducationEnum lawLearnerEducationEnum : LawLearnerEducationEnum.values()) {
                education.add(new ScreenVo(lawLearnerEducationEnum.getCode(), lawLearnerEducationEnum.getName(), 0, null));
            }
        }
        map.put("education", education);
        //人员类型
        List<ScreenVo> personType = bigScreenMapper.lawLearnerPersonType(areaCode);
        if (CollectionUtil.isNotEmpty(personType)) {
            for (LawLearnerPersonTypeEnum lawLearnerPersonTypeEnum : LawLearnerPersonTypeEnum.values()) {
                Optional<ScreenVo> first = personType.stream().filter(e -> lawLearnerPersonTypeEnum.getCode().equals(e.getCode())).findFirst();
                if (first.isPresent()) {
                    ScreenVo screenVo = first.get();
                    screenVo.setName(lawLearnerPersonTypeEnum.getName());
                } else {
                    personType.add(new ScreenVo(lawLearnerPersonTypeEnum.getCode(), lawLearnerPersonTypeEnum.getName(), 0, null));
                }
            }
        } else {
            personType = new ArrayList<>();
            for (LawLearnerPersonTypeEnum lawLearnerPersonTypeEnum : LawLearnerPersonTypeEnum.values()) {
                personType.add(new ScreenVo(lawLearnerPersonTypeEnum.getCode(), lawLearnerPersonTypeEnum.getName(), 0, null));
            }
        }
        map.put("personType", personType);


        return map;

    }

    @Override
    public PageResult<LawUserVo> lawCultivateUser(Integer code, String areaName) {
        //省级
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            Page<LawUserVo> page = bigScreenMapper.lawCultivateUser(PageFactory.defaultPage(), code, null);
            return new PageResult<>(page);
        }
        String orgId = getOrgIdByName(areaName);
        SysOrg sysOrg = sysOrgService.getById(orgId);
        if (ObjectUtil.isNotEmpty(sysOrg)) {
            if (sysOrg.getLevel() == 1) {
                return new PageResult<>(new Page<>());
            }
            if (sysOrg.getLevel() == 2) {
                SysOrg sysOrg1 = sysOrgService.getById(sysOrg.getPid());
                if (ObjectUtil.isNotEmpty(sysOrg1) && sysOrg1.getLevel() == 2) {
                    Set<String> deptIds = sysOrgService.getDeptIds(sysOrg1.getId());
                    Page<LawUserVo> page = bigScreenMapper.lawCultivateUser(PageFactory.defaultPage(), code, deptIds);
                    return new PageResult<>(page);
                }
            }
        }
        Set<String> deptIds = sysOrgService.getDeptIds(orgId);
        Page<LawUserVo> page = bigScreenMapper.lawCultivateUser(PageFactory.defaultPage(), code, deptIds);
        return new PageResult<>(page);
    }

    @Override
    public List<ScreenVo> lawResponsibility() {
        return bigScreenMapper.lawResponsibility();
    }

    @Override
    public PageResult<LeResources> lawDynamic(String areaName) {
        LeResourcesParam leResourcesParam = new LeResourcesParam();
        leResourcesParam.setSearchStatus(2);
        leResourcesParam.setPageNo(1);
        leResourcesParam.setPageSize(30);
        leResourcesParam.setSource("0");
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            leResourcesParam.setDeptId(CommonConstant.TOP_ORG_ID);
        }
        String orgId = getOrgIdByName(areaName);
//        for (CityEnum cityEnum : CityEnum.values()) {
//            if (cityEnum.getName().equals(areaName)) {
//                leResourcesParam.setDeptId(cityEnum.getOrgId());
//            }
//        }
        if (ObjectUtil.isNotEmpty(orgId)) {
            leResourcesParam.setDeptId(orgId);
        }
        return this.storePage(leResourcesParam);
    }

    public PageResult<LeResources> storePage(LeResourcesParam param) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();


        if (ObjectUtil.isNotNull(param)) {

            if (ObjectUtil.isNotEmpty(param.getId())) {
                String[] split = param.getId().split(",");
                queryWrapper.lambda().in(LeResources::getId, Arrays.asList(split));
            }
            // 根据资源类别 查询
            if (ObjectUtil.isNotEmpty(param.getType())) {
                queryWrapper.lambda().likeRight(LeResources::getType, param.getType());
            }
            if (ObjectUtil.isNotEmpty(param.getTypeList())) {
                queryWrapper.lambda().in(LeResources::getType, param.getTypeList());
            }

            if (ObjectUtil.isNotEmpty(param.getEliminateIds())) {
                String[] split = param.getEliminateIds().split(",");
                queryWrapper.lambda().notIn(LeResources::getId, Arrays.asList(split));
            }
            if (ObjectUtil.isNotEmpty(param.getSource())) {
                queryWrapper.lambda().eq(LeResources::getSource, param.getSource());
            }
            // 根据标题 查询
            if (ObjectUtil.isNotEmpty(param.getTitle())) {
                queryWrapper.lambda().like(LeResources::getTitle, param.getTitle());
            }
            // 根据单位id 查询
            if (ObjectUtil.isNotEmpty(param.getDeptId())) {
                Set<String> deptIds = sysOrgService.getDeptIds(param.getDeptId());
                queryWrapper.lambda().in(LeResources::getDeptId, deptIds);
            }

            if (ObjectUtil.isNotEmpty(param.getLabelId())) {
                queryWrapper.lambda().like(LeResources::getLabelIds, param.getLabelId());
            }
            // 是否配置栏目
            if (ObjectUtil.isNotEmpty(param.getContactColumn())) {
                if ("1".equals(param.getContactColumn())) {
                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                } else {
                    queryWrapper.lambda().and(i -> i.eq(LeResources::getStatus, 1)
                            .or().eq(LeResources::getStatus, 5)
                    );
//                    queryWrapper.lambda().isNotNull(LeResources::getColumnId);
                }
            }

            // 根据时间范围查询
            if (ObjectUtil.isAllNotEmpty(param.getSearchBeginTime(), param.getSearchEndTime())) {
                queryWrapper
                        .apply("date_format (create_time,'%Y-%m-%d') >= date_format('" + param.getSearchBeginTime()
                                + "','%Y-%m-%d')")
                        .apply("date_format (create_time,'%Y-%m-%d') <= date_format('" + param.getSearchEndTime()
                                + "','%Y-%m-%d')");
            }
        }
        queryWrapper.lambda().orderByDesc(LeResources::getCreateTime);
        Page<LeResources> page = PageFactory.defaultPage();
        page.setCurrent(param.getPageNo());
        page.setSize(param.getPageSize());
        PageResult<LeResources> pageResult = new PageResult<>(leResourcesService.page(page, queryWrapper));
        List<LeResources> rows = pageResult.getRows();
        if (param.getNeedFile() == 0 && CollectionUtil.isNotEmpty(rows)) {
            for (LeResources row : rows) {
                if (ObjectUtil.isNotEmpty(row.getCover())) {
                    row.setCoverInfo(sysFileInfoService.getById(row.getCover()));
                }
            }
        }
        return pageResult;
    }

    @Override
    public Map<String, Object> lawEducation(String areaName) {

        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaName = null;
        }
        //三度
        ExamResult threeDimensional = bigScreenMapper.threeDimensional(areaName);
        List<ScreenVo> threeDimensionalList = new ArrayList<>();
        threeDimensionalList.add(new ScreenVo(ThreeDimensionalEnum.ONE.getCode(), ThreeDimensionalEnum.ONE.getName(), 0, threeDimensional.getFirstAwareness().setScale(1, RoundingMode.HALF_UP)));
        threeDimensionalList.add(new ScreenVo(ThreeDimensionalEnum.TWO.getCode(), ThreeDimensionalEnum.TWO.getName(), 0, threeDimensional.getFirstIdentity().setScale(1, RoundingMode.HALF_UP)));
        threeDimensionalList.add(new ScreenVo(ThreeDimensionalEnum.THREE.getCode(), ThreeDimensionalEnum.THREE.getName(), 0, threeDimensional.getFirstParticipation().setScale(1, RoundingMode.HALF_UP)));

        map.put("threeDimensional", threeDimensionalList);
        //五法，其中普法默认91.7
        ExamResult fiveLaw = bigScreenMapper.fiveLaw(areaName);
        List<ScreenVo> fiveLawList = new ArrayList<>();
        fiveLawList.add(new ScreenVo(FiveLawEnum.ONE.getCode(), FiveLawEnum.ONE.getName(), 0, fiveLaw.getSecondRespectLaw().setScale(1, RoundingMode.HALF_UP)));
        fiveLawList.add(new ScreenVo(FiveLawEnum.TWO.getCode(), FiveLawEnum.TWO.getName(), 0, fiveLaw.getSecondAbidingLaw().setScale(1, RoundingMode.HALF_UP)));
        fiveLawList.add(new ScreenVo(FiveLawEnum.THREE.getCode(), FiveLawEnum.THREE.getName(), 0, fiveLaw.getSecondStudyLaw().setScale(1, RoundingMode.HALF_UP)));
        fiveLawList.add(new ScreenVo(FiveLawEnum.FOUR.getCode(), FiveLawEnum.FOUR.getName(), 0, fiveLaw.getSecondUseLaw().setScale(1, RoundingMode.HALF_UP)));
        fiveLawList.add(new ScreenVo(FiveLawEnum.FIVE.getCode(), FiveLawEnum.FIVE.getName(), 0, new BigDecimal("91.7")));
        map.put("fiveLaw", fiveLawList);

        return map;
    }

    @Override
    public List<ScreenVo> lawFunds(String areaName) {
        int year = DateUtil.thisYear();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            return bigScreenMapper.lawFunds(year);
        } else {
            List<ScreenVo> screenVoList = bigScreenMapper.lawFundsArea(year, areaName);
            List<ScreenVo> screenVos = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(screenVoList)) {
                for (ScreenVo screenVo : screenVoList) {
                    if (ObjectUtil.isNotNull(screenVo)) {
                        screenVos.add(screenVo);
                    }
                }
            }
            return screenVos;
        }
    }

    @Override
    public List<JSONObject> lawResource(String areaName) {
        List<JSONObject> list = new ArrayList<>();
        List<ResourceVo> screenVoList = Collections.emptyList();
        List<ResourceVo> detailVoList = Collections.emptyList();
        if (!CommonConstant.TOP_AREA_NAME.equals(areaName)) {
//            CityEnum cityEnum = CityEnum.getEnumByName(areaName);
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                Set<String> deptIds = sysOrgService.getDeptIds(orgId);
                screenVoList = bigScreenMapper.lawResource(deptIds);
                detailVoList = bigScreenMapper.lawResourceDetail(deptIds);
            }
        } else {
            screenVoList = bigScreenMapper.lawResource(null);
            detailVoList = bigScreenMapper.lawResourceDetail(null);
        }
        if (CollectionUtil.isNotEmpty(screenVoList)) {
            for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", resourceTypeEnum.getName());
                Optional<ResourceVo> first = screenVoList.stream().filter(e -> resourceTypeEnum.getCode().equals(e.getCode())).findFirst();
                if (first.isPresent()) {
                    ResourceVo resourceVo = first.get();
                    jsonObject.put("value", Optional.of(resourceVo.getValue()));
                    jsonObject.put("ycy", Optional.of(resourceVo.getYcy()));
                    jsonObject.put("wcy", Optional.of(resourceVo.getWcy()));
                    jsonObject.put("pfzrz", Optional.of(resourceVo.getPfzrz()));
                    jsonObject.put("sfxz", Optional.of(resourceVo.getSfxz()));
                } else {
                    jsonObject.put("value", 0);
                    jsonObject.put("ycy", 0);
                    jsonObject.put("wcy", 0);
                    jsonObject.put("pfzrz", 0);
                    jsonObject.put("sfxz", 0);
                }
                List<JSONObject> childList = new ArrayList<>();
                for (ResourceTypeDetailEnum resourceTypeDetailEnum : ResourceTypeDetailEnum.values()) {
                    if (Objects.equals(resourceTypeDetailEnum.getPcode(), resourceTypeEnum.getCode())) {
                        Optional<ResourceVo> first1 = detailVoList.stream().filter(e -> resourceTypeDetailEnum.getCode().equals(e.getCode())).findFirst();
                        if (first1.isPresent()) {
                            ResourceVo resourceVo = first1.get();
                            JSONObject childObject = new JSONObject();
                            childObject.put("name", resourceTypeDetailEnum.getName());
                            childObject.put("value", Optional.of(resourceVo.getValue()));
                            childList.add(childObject);
                        } else {
                            JSONObject childObject = new JSONObject();
                            childObject.put("name", resourceTypeDetailEnum.getName());
                            childObject.put("value", 0);
                            childList.add(childObject);
                        }
                    }
                }
                jsonObject.put("child", childList);
                list.add(jsonObject);
            }
        } else {
            //按照上面的循环，没有数据，填充0
            for (ResourceTypeEnum resourceTypeEnum : ResourceTypeEnum.values()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", resourceTypeEnum.getName());
                jsonObject.put("value", 0);
                for (ResourceTypeDetailEnum resourceTypeDetailEnum : ResourceTypeDetailEnum.values()) {
                    if (Objects.equals(resourceTypeDetailEnum.getPcode(), resourceTypeEnum.getCode())) {
                        JSONObject childObject = new JSONObject();
                        childObject.put("name", resourceTypeDetailEnum.getName());
                        childObject.put("value", 0);
                        jsonObject.put("child", childObject);
                    }
                }
                list.add(jsonObject);
            }
        }
        return list;

    }

    @Override
    public List<JSONObject> pointInfo(String name, Integer type, String orgId) {
        if (ObjectUtil.isEmpty(orgId)) {
            SysLoginUser sysLoginUser = LoginContextHolder.me().getSysLoginUser();
            if (sysLoginUser.getLevel() == 1) {
                List<JSONObject> jsonObjects = bigScreenMapper.pointInfo(name, null, null);
                if (CollectionUtil.isNotEmpty(jsonObjects)) {
                    if (ObjectUtil.isNotEmpty(type)) {
                        return jsonObjects.stream().filter(e -> type.equals(e.getInteger("type"))).collect(Collectors.toList());
                    }
                    return jsonObjects;
                }
            }
            orgId = LoginContextHolder.me().getSysLoginUserOrgId();
        }
        Set<String> deptIds = sysOrgService.getDeptIds(orgId);
        SysOrg sysOrg = sysOrgService.getById(orgId);
        List<JSONObject> jsonObjects = bigScreenMapper.pointInfo(name, deptIds, sysOrg.getAreaCode());
        if (CollectionUtil.isNotEmpty(jsonObjects)) {
            if (ObjectUtil.isNotEmpty(type)) {
                return jsonObjects.stream().filter(e -> type.equals(e.getInteger("type"))).collect(Collectors.toList());
            }
            return jsonObjects;
        }
        return Collections.emptyList();
    }

    @Override
    public List<LawPositionVo> overview() {
        List<ScreenVo> screenVos = lawPositionService.overview();

        List<ScreenVo> pointList = bigScreenMapper.pointCount();

        List<LawPositionVo> list = new ArrayList<>();
        List<String> cityList = CommonConstant.CITY_LIST;
        for (String city : cityList) {
            LawPositionVo lawPositionVo = new LawPositionVo();
            lawPositionVo.setCity(city);
            Optional<ScreenVo> first = screenVos.stream().filter(e -> city.equals(e.getName()) && LawPositionTypeEnum.FOUR.getCode().equals(e.getCode())).findFirst();
            if (first.isPresent()) {
                ScreenVo screenVo = first.get();
                lawPositionVo.setMzfzsfc(screenVo.getValue());
            }

            Optional<ScreenVo> second = screenVos.stream().filter(e -> city.equals(e.getName()) && LawPositionTypeEnum.THREE.getCode().equals(e.getCode())).findFirst();
            if (second.isPresent()) {
                ScreenVo screenVo = second.get();
                lawPositionVo.setFzxcjyjd(screenVo.getValue());
            }

            Optional<ScreenVo> first1 = pointList.stream().filter(e -> city.equals(e.getName())).findFirst();
            if (first1.isPresent()) {
                ScreenVo screenVo = first1.get();
                lawPositionVo.setSjgcd(screenVo.getValue());
            }

            list.add(lawPositionVo);

        }


        return list;
    }

    @Override
    public List<JSONObject> lawPositionDetail(String areaName, Integer type) {
        List<JSONObject> list = new ArrayList<>();
        List<String> areaNameList = new ArrayList<>();

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaNameList = CommonConstant.CITY_LIST;
            if (type == 3) {
                list = bigScreenMapper.lawPositionDetail(null);
            }
            if (type == 4 || type == 2 || type == 1) {
                list = bigScreenMapper.lawPositionDetailNum(type);
            }
            if (type == 5) {
                list = bigScreenMapper.obPointCount();
            }
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            areaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
            if (type == 3) {
                list = bigScreenMapper.lawPositionDetail(areaName);
            }
            if (type == 4 || type == 2 || type == 1) {
                list = bigScreenMapper.lawPositionDetailNumArea(areaName, type);
            }
            if (type == 5) {
                list = bigScreenMapper.obPointCountArea(areaName);
            }
        } else {
            areaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
            if (type == 3) {
                list = bigScreenMapper.lawPositionDetailArea(areaName);
            }
            if (type == 4 || type == 2 || type == 1) {
                list = bigScreenMapper.lawPositionDetailNumAreaQx(areaName, type);
            }
            if (type == 5) {
                list = bigScreenMapper.obPointCountAreaQx(areaName);
            }
            return list;
        }
        if (CollectionUtil.isNotEmpty(areaNameList)) {
            if (CollectionUtil.isNotEmpty(list)) {
                for (String area : areaNameList) {
                    if (type == 3) {
                        Optional<JSONObject> name = list.stream().filter(e -> area.equals(e.getString("city"))).findFirst();
                        if (!name.isPresent()) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("city", area);
                            jsonObject.put("num", "0");
                            list.add(jsonObject);
                        }
                    } else {
                        Optional<JSONObject> name = list.stream().filter(e -> area.equals(e.getString("city"))).findFirst();
                        if (!name.isPresent()) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("city", area);
                            jsonObject.put("num", "0");
                            list.add(jsonObject);
                        }
                    }

                }
            } else {
                for (String area : areaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("city", area);
                    jsonObject.put("num", "0");
                    list.add(jsonObject);
                }
            }
        }

        return list;


    }

    @Override
    public List<JSONObject> metrics(JSONObject jsonObject) {
        SysUser admin = sysUserService.getUserByCount("admin");
        authService.doLogin(admin);
        JSONArray muduleNames = jsonObject.getJSONArray("muduleName");
        if (muduleNames.isEmpty()) {
            muduleNames.add("普法阵地");
            muduleNames.add("普法干部队伍");
            muduleNames.add("法律明白人");
            muduleNames.add("普法动态");
            muduleNames.add("公民法治素养");
            muduleNames.add("普法经费");
            muduleNames.add("普法资源");
            muduleNames.add("普法责任制");
        }
        List<JSONObject> list = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(muduleNames)) {
            for (Object muduleName : muduleNames) {
                String name = muduleName.toString().trim();
                switch (name) {
                    case "普法阵地":
//                        List<ScreenVo> screenVos = this.lawPosition(CommonConstant.TOP_AREA_NAME);
//                        if (CollectionUtil.isNotEmpty(screenVos)) {
//                            List<MetricsVo> pfzd = new ArrayList<>();
//                            for (ScreenVo screenVo : screenVos) {
//                                MetricsVo metricsVo = new MetricsVo();
//                                metricsVo.setName(screenVo.getName());
//                                Map<String, Integer> map = new HashMap<>();
//                                map.put("数量", screenVo.getValue());
//                                metricsVo.setContent(map);
//                                pfzd.add(metricsVo);
//                            }
//                            JSONObject pfzdJson = new JSONObject();
//                            pfzdJson.put("name", "普法阵地");
//                            pfzdJson.put("metrics", pfzd);
//                            list.add(pfzdJson);
//                        }
                        JSONObject pfzdJson = new JSONObject();
                        pfzdJson.put("name", "普法阵地");
                        pfzdJson.put("metrics", JSONObject.parseArray("[\n" +
                                "                {\n" +
                                "                    \"name\": \"国家级民主法治示范村(社区)\",\n" +
                                "                    \"content\": {\n" +
                                "                        \"数量\": 272\n" +
                                "                    },\n" +
                                "                    \"live_update\": false\n" +
                                "                },\n" +
                                "                {\n" +
                                "                    \"name\": \"全国法治宣传教育基地\",\n" +
                                "                    \"content\": {\n" +
                                "                        \"数量\": 10\n" +
                                "                    },\n" +
                                "                    \"live_update\": false\n" +
                                "                },\n" +
                                "                {\n" +
                                "                    \"name\": \"省级民主法治村\",\n" +
                                "                    \"content\": {\n" +
                                "                        \"数量\": 5449\n" +
                                "                    },\n" +
                                "                    \"live_update\": false\n" +
                                "                },\n" +
                                "                {\n" +
                                "                    \"name\": \"省级公民法治素养观测点\",\n" +
                                "                    \"content\": {\n" +
                                "                        \"数量\": 234\n" +
                                "                    },\n" +
                                "                    \"live_update\": false\n" +
                                "                },\n" +
                                "                {\n" +
                                "                    \"name\": \"各级各类法治宣传教育基地\",\n" +
                                "                    \"content\": {\n" +
                                "                        \"数量\": 6640\n" +
                                "                    },\n" +
                                "                    \"live_update\": false\n" +
                                "                }\n" +
                                "            ]"));
                        list.add(pfzdJson);
                        break;
                    case "普法干部队伍":
                        Map<String, Object> stringObjectMap = this.lawCultivate(CommonConstant.TOP_AREA_NAME);
                        if (MapUtil.isNotEmpty(stringObjectMap)) {
                            JSONObject pfgbdwJson = new JSONObject();
                            List<MetricsVo> pfgbdw = new ArrayList<>();
                            pfgbdwJson.put("name", "普法干部队伍");

                            //男女比例
                            Object femaleAndMale = stringObjectMap.get("femaleAndMale");
                            List<ScreenVo> pfgbdwSex = JSONObject.parseArray(JSONObject.toJSONString(femaleAndMale), ScreenVo.class);
                            if (CollectionUtil.isNotEmpty(pfgbdwSex)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("男女比例");
                                Map<String, Integer> map = new HashMap<>();
                                for (ScreenVo sex : pfgbdwSex) {
                                    map.put(sex.getName(), sex.getValue());
                                }
                                metricsVo.setContent(map);
                                pfgbdw.add(metricsVo);
                                pfgbdwJson.put("metrics", pfgbdw);
//                                list.add(pfgbdwJson);
                            }
                            //省市区分布
                            Object provinceAndCity = stringObjectMap.get("provinceAndCity");
                            List<ScreenVo> pfgbdwArea = JSONObject.parseArray(JSONObject.toJSONString(provinceAndCity), ScreenVo.class);
                            if (CollectionUtil.isNotEmpty(pfgbdwArea)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("省市区分布");
                                Map<String, Integer> map = new HashMap<>();
                                for (ScreenVo area : pfgbdwArea) {
                                    map.put(area.getName(), area.getValue());
                                }
                                metricsVo.setContent(map);
                                pfgbdw.add(metricsVo);
                                pfgbdwJson.put("metrics", pfgbdw);
                                list.add(pfgbdwJson);
                            }
                        }
                        break;
                    case "法律明白人":
                        Map<String, Object> flmbrMap = this.lawLearner(CommonConstant.TOP_AREA_NAME);
                        if (MapUtil.isNotEmpty(flmbrMap)) {
                            JSONObject flmbrJson = new JSONObject();
                            List<MetricsVo> flmbr = new ArrayList<>();
                            flmbrJson.put("name", "法律明白人");

                            //政治面貌
                            Object political = flmbrMap.get("political");
                            List<ScreenVo> flmbrPolitical = JSONObject.parseArray(JSONObject.toJSONString(political), ScreenVo.class);
                            if (CollectionUtil.isNotEmpty(flmbrPolitical)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("政治面貌");
                                Map<String, Integer> map = new HashMap<>();
                                for (ScreenVo politicals : flmbrPolitical) {
                                    // 添加空值检查
                                    if (politicals != null && politicals.getName() != null) {
                                        map.put(politicals.getName(), politicals.getValue());
                                    }
                                }
                                // 只有当map不为空时才添加
                                if (!map.isEmpty()) {
                                    metricsVo.setContent(map);
                                    flmbr.add(metricsVo);
                                }
                            }

                            //学历
                            Object education = flmbrMap.get("education");
                            List<ScreenVo> flmbrEducation = JSONObject.parseArray(JSONObject.toJSONString(education), ScreenVo.class);
                            if (CollectionUtil.isNotEmpty(flmbrEducation)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("学历");
                                Map<String, Integer> map = new HashMap<>();
                                for (ScreenVo educations : flmbrEducation) {
                                    // 添加空值检查
                                    if (educations != null && educations.getName() != null) {
                                        map.put(educations.getName(), educations.getValue());
                                    }
                                }
                                // 只有当map不为空时才添加
                                if (!map.isEmpty()) {
                                    metricsVo.setContent(map);
                                    flmbr.add(metricsVo);
                                }
                            }

                            //人员类型
                            Object personType = flmbrMap.get("personType");
                            List<ScreenVo> flmbrPersonType = JSONObject.parseArray(JSONObject.toJSONString(personType), ScreenVo.class);
                            if (CollectionUtil.isNotEmpty(flmbrPersonType)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("人员类型");
                                Map<String, Integer> map = new HashMap<>();
                                for (ScreenVo personTypes : flmbrPersonType) {
                                    // 添加空值检查
                                    if (personTypes != null && personTypes.getName() != null) {
                                        map.put(personTypes.getName(), personTypes.getValue());
                                    }
                                }
                                // 只有当map不为空时才添加
                                if (!map.isEmpty()) {
                                    metricsVo.setContent(map);
                                    flmbr.add(metricsVo);
                                }
                            }
                            //全省法律明白人，固定数值190129
                            MetricsVo metricsVo = new MetricsVo();
                            metricsVo.setName("全省法律明白人");
                            Map<String, Integer> map = new HashMap<>();
                            map.put("数量", this.lawLearnerTotal(CommonConstant.TOP_AREA_NAME));
                            metricsVo.setContent(map);
                            flmbr.add(metricsVo);


                            // 只有当flmbr不为空时才添加到结果中
                            if (!flmbr.isEmpty()) {
                                flmbrJson.put("metrics", flmbr);
                                list.add(flmbrJson);
                            }

                        }
                        break;
                    case "普法动态":
                        PageResult<LeResources> leResourcesPageResult = this.lawDynamic(CommonConstant.TOP_AREA_NAME);
                        List<LeResources> resources = leResourcesPageResult.getRows();
                        if (CollectionUtil.isNotEmpty(resources)) {
                            JSONObject lawDynamicJson = new JSONObject();
                            lawDynamicJson.put("name", "普法动态");
                            List<JSONObject> lawDynamicList = new ArrayList<>();
                            resources.forEach(resource -> {
                                JSONObject result = new JSONObject();
                                result.put("信息", resource.getTitle());
                                result.put("单位", resource.getDeptName());
                                result.put("时间", resource.getCreateTime());
                                lawDynamicList.add(result);
                            });
                            List<MetricsVo> lawDynamic = new ArrayList<>();
                            MetricsVo metricsVo = new MetricsVo();
                            metricsVo.setName("普法动态");
                            metricsVo.setContent(lawDynamicList);
                            lawDynamic.add(metricsVo);
                            lawDynamicJson.put("metrics", lawDynamic);
                            list.add(lawDynamicJson);
                        }

                        break;
                    case "公民法治素养":
                        Map<String, Object> lawedEducationMap = this.lawEducation(CommonConstant.TOP_AREA_NAME);
                        if (MapUtil.isNotEmpty(lawedEducationMap)) {
                            JSONObject lawedEducationJson = new JSONObject();
                            List<MetricsVo> lawedEducation = new ArrayList<>();
                            lawedEducationJson.put("name", "公民法治素养");
                            //地方版、行业版、通识版数量
                            List<JSONObject> lawEducationVersionCount = this.lawEducationVersionCount(CommonConstant.TOP_AREA_NAME);
                            if (CollectionUtil.isNotEmpty(lawEducationVersionCount)) {
                                JSONObject lawEducationVersionCountJson = lawEducationVersionCount.get(0);
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("地方版、行业版、通识版数量");
                                Map<String, Integer> map = new HashMap<>();
                                map.put("地方版", lawEducationVersionCountJson.getInteger("tsb"));
                                map.put("行业版", lawEducationVersionCountJson.getInteger("dfb"));
                                map.put("通识版", lawEducationVersionCountJson.getInteger("hyb"));
                                metricsVo.setContent(map);
                                lawedEducation.add(metricsVo);
                                lawedEducationJson.put("metrics", lawedEducation);
                            }
                            //各地市数量分布
                            List<JSONObject> lawEducationCityDistribution = this.lawEducationCityDistribution(CommonConstant.TOP_AREA_NAME);
                            if (CollectionUtil.isNotEmpty(lawEducationCityDistribution)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("各地市数量分布");
                                Map<String, Map<String, Object>> map = new HashMap<>();
                                for (JSONObject object : lawEducationCityDistribution) {
                                    Map<String, Object> dsMap = new HashMap<>();
                                    dsMap.put("地方版", object.getInteger("tsb"));
                                    dsMap.put("行业版", object.getInteger("dfb"));
                                    dsMap.put("通识版", object.getInteger("hyb"));
                                    map.put(object.getString("city"), dsMap);
                                }
                                metricsVo.setContent(map);
                                lawedEducation.add(metricsVo);
                                lawedEducationJson.put("metrics", lawedEducation);
                            }
                            //词云
                            List<String> lawEducationWordCloud = this.lawEducationCityDistributionKeyword(CommonConstant.TOP_AREA_NAME);
                            if (CollectionUtil.isNotEmpty(lawEducationWordCloud)) {
                                MetricsVo metricsVo = new MetricsVo();
                                metricsVo.setName("词云");
                                metricsVo.setContent(lawEducationWordCloud);
                                lawedEducation.add(metricsVo);
                                lawedEducationJson.put("metrics", lawedEducation);
                            }
                            list.add(lawedEducationJson);
//                             //三度
//                             Object threeDegree = lawedEducationMap.get("threeDimensional");
//                             List<ScreenVo> lawedEducationThreeDegree = JSONObject.parseArray(JSONObject.toJSONString(threeDegree), ScreenVo.class);
//                             if (CollectionUtil.isNotEmpty(lawedEducationThreeDegree)) {
//                                 MetricsVo metricsVo = new MetricsVo();
//                                 metricsVo.setName("三度");
//                                 Map<String, Double> map = new HashMap<>();
// //                                for (ScreenVo threeDegrees : lawedEducationThreeDegree) {
// //                                    map.put(threeDegrees.getName(), threeDegrees.getValue());
// //                                }
//                                 map.put("知晓度", 83.4);
//                                 map.put("认同度", 91.8);
//                                 map.put("参与度", 90.7);
//                                 metricsVo.setContent(map);
//                                 lawedEducation.add(metricsVo);
//                                 lawedEducationJson.put("metrics", lawedEducation);
//                                 list.add(lawedEducationJson);
//                             }
//                             //五法
//                             Object fiveLaw = lawedEducationMap.get("fiveLaw");
//                             List<ScreenVo> lawedEducationFiveLaw = JSONObject.parseArray(JSONObject.toJSONString(fiveLaw), ScreenVo.class);
//                             if (CollectionUtil.isNotEmpty(lawedEducationFiveLaw)) {
//                                 MetricsVo metricsVo = new MetricsVo();
//                                 metricsVo.setName("五法");
//                                 Map<String, Double> map = new HashMap<>();
// //                                for (ScreenVo fiveLaws : lawedEducationFiveLaw) {
// //                                    map.put(fiveLaws.getName(), fiveLaws.getValue());
// //                                }
//                                 map.put("学法", 78.2);
//                                 map.put("守法", 93.2);
//                                 map.put("用法", 81.5);
//                                 map.put("普法", 91.7);
//                                 map.put("尊法", 89.8);
//                                 metricsVo.setContent(map);
//                                 lawedEducation.add(metricsVo);
//                                 lawedEducationJson.put("metrics", lawedEducation);
//                                 list.add(lawedEducationJson);
//                             }
                        }

                        break;
                    case "普法经费":
                        List<ScreenVo> lawedFunds = this.lawFunds(CommonConstant.TOP_AREA_NAME);
                        if (CollectionUtil.isNotEmpty(lawedFunds)) {
                            JSONObject lawedFundsJson = new JSONObject();
                            List<MetricsVo> lawedFundsList = new ArrayList<>();
                            lawedFundsJson.put("name", "普法经费");
                            MetricsVo metricsVo = new MetricsVo();
                            metricsVo.setName("普法经费");
                            Map<String, BigDecimal> map = new HashMap<>();
                            for (ScreenVo funds : lawedFunds) {
                                map.put(funds.getName(), funds.getScore());
                            }
                            metricsVo.setContent(map);
                            lawedFundsList.add(metricsVo);
                            lawedFundsJson.put("metrics", lawedFundsList);
                            list.add(lawedFundsJson);
                        }
                        break;
                    case "普法资源":
                        List<JSONObject> lawedResource = this.lawResource(CommonConstant.TOP_AREA_NAME);
                        if (CollectionUtil.isNotEmpty(lawedResource)) {
                            JSONObject lawedResourceJson = new JSONObject();
                            List<MetricsVo> lawedResourceList = new ArrayList<>();
                            lawedResourceJson.put("name", "普法资源");
                            MetricsVo metricsVo = new MetricsVo();
                            metricsVo.setName("普法资源");
                            Map<String, Integer> map = new HashMap<>();

                            metricsVo.setContent(lawedResource);
                            lawedResourceList.add(metricsVo);
                            lawedResourceJson.put("metrics", lawedResourceList);
                            list.add(lawedResourceJson);
                        }
                        break;
                    case "普法责任制":
                        List<ScreenVo> lawedResponsibility = this.lawResponsibility();
                        if (CollectionUtil.isNotEmpty(lawedResponsibility)) {
                            JSONObject lawedResponsibilityJson = new JSONObject();
                            List<MetricsVo> lawedResponsibilityList = new ArrayList<>();
                            lawedResponsibilityJson.put("name", "普法责任制");
                            MetricsVo metricsVo = new MetricsVo();
                            metricsVo.setName("普法责任制");
                            Map<String, Integer> map = new HashMap<>();
                            for (ScreenVo responsibility : lawedResponsibility) {
                                map.put(responsibility.getName(), responsibility.getValue());
                            }
                            metricsVo.setContent(map);
                            lawedResponsibilityList.add(metricsVo);
                            lawedResponsibilityJson.put("metrics", lawedResponsibilityList);
                            list.add(lawedResponsibilityJson);
                        }
                        break;
                }
            }
        }
        return list;
    }

    @Override
    public List<JSONObject> metricsLawPosition() {

        List<JSONObject> jsonObjects = bigScreenMapper.pointInfoMetrics(null);
        if (CollectionUtil.isNotEmpty(jsonObjects)) {
            for (JSONObject object : jsonObjects) {
                Integer type = object.getInteger("type");
                switch (type) {
                    case 1:
                        object.put("typeName", "省级民主法治村");
                        break;
                    case 2:
                        object.put("typeName", "法治文化阵地");
                        break;
                    case 3:
                        object.put("typeName", "全国法治宣传教育基地");
                        break;
                    case 4:
                        object.put("typeName", "国家级民主法治示范村（社区）");
                        break;
                    case 5:
                        object.put("typeName", "观测点");
                        break;
                    case 6:
                        object.put("typeName", "双普教育基地");
                        break;
                }
                String name = object.getString("name");
                if (ObjectUtil.isNotEmpty(name)) {
                    //去除中文双引号
                    String replace = name.replace("\"", "").replace("\"", "");
                    object.put("name", replace);
                }

            }
        }
        return jsonObjects;
    }

    @Override
    public List<JSONObject> lawEducationCityDistribution(String areaName) {
        String areaCode = getAreaCodeByName(areaName);
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            List<JSONObject> list = bigScreenMapper.lawEducationCityDistribution();
            //根据CommonConstant.CITY_LIST的顺序重新排序
            List<JSONObject> result = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                for (JSONObject jsonObject : list) {
                    if (city.equals(jsonObject.getString("city"))) {
                        result.add(jsonObject);
                        break;
                    }
                }
            }
            return result;
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            return bigScreenMapper.lawEducationCityDistributionCity(areaCode);
        } else {
            return bigScreenMapper.lawEducationCityDistributionQx(areaCode);
        }
    }

    @Override
    public List<String> lawEducationCityDistributionKeyword(String city) {
        if (CommonConstant.TOP_AREA_NAME.equals(city)) {
            return bigScreenMapper.lawEducationCityDistributionKeyword(null);
        }
        if (CommonConstant.CITY_LIST.contains(city)) {
            return bigScreenMapper.lawEducationCityDistributionKeyword(city);
        }
        return bigScreenMapper.lawEducationCityDistributionKeywordQx(city);
    }

    @Override
    public List<JSONObject> lawEducationVersionCount(String areaName) {
        //WHEN version_type = 1 THEN 'tsb'
        //WHEN version_type = 2 THEN 'dfb'
        //WHEN version_type = 3 THEN 'hyb'
        List<JSONObject> list = this.lawEducationCityDistribution(areaName);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tsb", 0);
        jsonObject.put("dfb", 0);
        jsonObject.put("hyb", 0);
        //根据数组里面的数字求和
        int tsb = 0;
        int dfb = 0;
        int hyb = 0;
        for (JSONObject object : list) {
            if (ObjectUtil.isNotEmpty(object.getInteger("tsb"))) {
                tsb += object.getInteger("tsb");
            }
            if (ObjectUtil.isNotEmpty(object.getInteger("dfb"))) {
                dfb += object.getInteger("dfb");
            }
            if (ObjectUtil.isNotEmpty(object.getInteger("hyb"))) {
                hyb += object.getInteger("hyb");
            }
        }
        jsonObject.put("tsb", tsb);
        jsonObject.put("dfb", dfb);
        jsonObject.put("hyb", hyb);
        List<JSONObject> result = new ArrayList<>();
        result.add(jsonObject);
        return result;

//        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
//            return bigScreenMapper.lawEducationVersionCount();
//        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
//            return bigScreenMapper.lawEducationVersionCountCity(areaName);
//        }else {
//            return bigScreenMapper.lawEducationVersionCountQx(areaName);
//        }

    }

    @Override
    public ResourceVo lawResourceFourType(String areaName) {
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            return bigScreenMapper.lawResourceFourType(null);
        }
        String orgId = getOrgIdByName(areaName);
        if (ObjectUtil.isNotEmpty(orgId)) {
            Set<String> deptIds = sysOrgService.getDeptIds(orgId);
            return bigScreenMapper.lawResourceFourType(deptIds);
        }
        //orgId为空，返回空值
        ResourceVo resourceVo = new ResourceVo();
        resourceVo.setYcy(0);
        resourceVo.setWcy(0);
        resourceVo.setPfzrz(0);
        resourceVo.setSfxz(0);
        return resourceVo;
    }

    @Override
    public int lawLearnerTotal(String areaName) {
        String areaCode = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCode)) {
            return bigScreenMapper.lawLearnerTotal(areaCode);
        }
        return 0;
    }

    @Override
    public Map<String, Object> villageList(String areaName) {
        String areaCode = getAreaCodeByName(areaName);

        List<LawPosition> list = bigScreenMapper.villageList(areaCode);
        Map<String, Object> map = new HashMap<>();
        if (CollectionUtil.isNotEmpty(list)) {
            map.put("list", list);
            map.put("total", list.size());
            return map;
        }
        map.put("list", new ArrayList<>());
        map.put("total", 0);
        return map;
    }

    @Override
    public List<JSONObject> cultureList(String areaName) {
        List<LawPosition> list = lawPositionService.list(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12).like(LawPosition::getAddressCode, getAreaCodeByName(areaName)));
        List<JSONObject> jsonObjectList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(list)) {
            //遍历枚举，配置数量和列表
            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                int count = 0;
                List<LawPosition> lawPositionList = list.stream().filter(e -> Objects.equals(e.getPositionType(), lawPositionTypeEnum.getCode())).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(lawPositionList)) {
                    count = lawPositionList.size();
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", lawPositionTypeEnum.getDescription());
                jsonObject.put("value", count);
                jsonObject.put("list", lawPositionList);
                jsonObjectList.add(jsonObject);
            }
        } else {
            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", lawPositionTypeEnum.getDescription());
                jsonObject.put("value", 0);
                jsonObject.put("list", new ArrayList<>());
                jsonObjectList.add(jsonObject);
            }
        }
        return jsonObjectList;
    }

    @Override
    public List<JSONObject> lawReport(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 全省统计，按地市分组
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject cityData = new JSONObject();
                cityData.put("city", city);

                // 获取该地市的组织机构ID
                String orgId = getOrgIdByName(city);
                Set<String> deptIds = null;
                if (ObjectUtil.isNotEmpty(orgId)) {
                    deptIds = sysOrgService.getDeptIds(orgId);
                }

                // 构建查询条件
                QueryWrapper<LeResources> query = new QueryWrapper<>();
                query.lambda().in(LeResources::getStatus, 1, 2, 5, 6) // 只统计有效资源
                        .eq(LeResources::getSource, "0"); // 只统计source_='0'的资源

                if (ObjectUtil.isNotEmpty(deptIds)) {
                    query.lambda().in(LeResources::getDeptId, deptIds);
                }

                int count = leResourcesService.count(query);
                cityData.put("count", count);
                resultList.add(cityData);
            }
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 地市级统计，按区县分组
            // 获取该地市下的所有区县
            List<String> districts = getDistrictsByCity(areaName);

            for (String district : districts) {
                JSONObject districtData = new JSONObject();
                districtData.put("area", district);

                // 获取该区县的组织机构ID
                String orgId = getOrgIdByName(district);
                Set<String> deptIds = null;
                if (ObjectUtil.isNotEmpty(orgId)) {
                    deptIds = sysOrgService.getDeptIds(orgId);
                }

                // 构建查询条件
                QueryWrapper<LeResources> query = new QueryWrapper<>();
                query.lambda().in(LeResources::getStatus, 1, 2, 5, 6) // 只统计有效资源
                        .eq(LeResources::getSource, "0"); // 只统计source_='0'的资源

                if (ObjectUtil.isNotEmpty(deptIds)) {
                    query.lambda().in(LeResources::getDeptId, deptIds);
                }

                int count = leResourcesService.count(query);
                districtData.put("count", count);
                resultList.add(districtData);
            }
        } else {
            // 区县级统计，返回该区县的统计
            JSONObject areaData = new JSONObject();
            areaData.put("area", areaName);

            // 获取该区县的组织机构ID
            String orgId = getOrgIdByName(areaName);
            Set<String> deptIds = null;
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }

            // 构建查询条件
            QueryWrapper<LeResources> query = new QueryWrapper<>();
            query.lambda().in(LeResources::getStatus, 1, 2, 5, 6) // 只统计有效资源
                    .eq(LeResources::getSource, "0"); // 只统计source_='0'的资源

            if (ObjectUtil.isNotEmpty(deptIds)) {
                query.lambda().in(LeResources::getDeptId, deptIds);
            }

            int count = leResourcesService.count(query);
            areaData.put("count", count);
            resultList.add(areaData);
        }

        return resultList;
    }

    @Override
    public Map<String, Object> villageTotal(String areaName) {
        Map<String, Object> map = new HashMap<>();
        //如果是省级，返回总数和各地市数量
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            map.put("total", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4)));
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().eq(LawPosition::getPositionType, 4).eq(LawPosition::getCity, city)));
                jsonObjectList.add(jsonObject);
            }
            map.put("cityList", jsonObjectList);
            return map;
        }

        String areaCode = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCode)) {
            map.put("total", bigScreenMapper.villageTotal(areaCode));
            map.put("cityList", bigScreenMapper.villageTotalCity(areaCode));
            return map;
        }
        map.put("total", 0);
        map.put("cityList", new ArrayList<>());
        return map;
    }

    @Override
    public List<LawPosition> villageSearch(String name) {
        return lawPositionService.list(new QueryWrapper<LawPosition>().lambda().and(e -> e.like(LawPosition::getPositionName, name).or().like(LawPosition::getCity, name).or().like(LawPosition::getArea, name)).eq(LawPosition::getPositionType, 4));
    }

    @Override
    public Map<String, Object> cultureTotal(String areaName) {
        Map<String, Object> map = new HashMap<>();
        String areaCodeByName = getAreaCodeByName(areaName);
        if (ObjectUtil.isNotEmpty(areaCodeByName)) {
            // 准备查询条件和结果容器
            Map<String, Object> provinceMap = new HashMap<>();
            Map<String, Object> cityMap = new HashMap<>();
            map.put("province", provinceMap);
            map.put("provinceName", "全国法治文化教育基地");
            map.put("city", cityMap);
            map.put("cityName", "省级法治文化教育基地");

            // 省级和市级的总数查询条件
            QueryWrapper<LawPosition> provinceQuery = new QueryWrapper<>();
            provinceQuery.lambda().eq(LawPosition::getPositionLevel, 1)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            QueryWrapper<LawPosition> cityQuery = new QueryWrapper<>();
            cityQuery.lambda().eq(LawPosition::getPositionLevel, 2)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            if (ObjectUtil.isNotEmpty(areaCodeByName)) {
                provinceQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
                cityQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
            }

            // 执行总数查询
            provinceMap.put("provinceTotal", lawPositionService.count(provinceQuery));
            cityMap.put("cityTotal", lawPositionService.count(cityQuery));

            // 批量查询各类型数量
            // 1. 构建查询条件 - 查询所有符合条件的数据
            QueryWrapper<LawPosition> allTypesQuery = new QueryWrapper<>();
            allTypesQuery.lambda()
                    .in(LawPosition::getPositionLevel, 1, 2)
                    .in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);

            if (ObjectUtil.isNotEmpty(areaCodeByName)) {
                allTypesQuery.lambda().like(LawPosition::getAddressCode, areaCodeByName);
            }

            // 2. 执行一次查询获取所有数据
            List<LawPosition> allPositions = lawPositionService.list(allTypesQuery);

            // 3. 在内存中处理数据分类统计
            Map<String, Integer> provinceTypeCountMap = new HashMap<>();
            Map<String, Integer> cityTypeCountMap = new HashMap<>();

            // 初始化计数器，确保结果中包含所有类型
            for (LawPositionBaseEnum typeEnum : LawPositionBaseEnum.values()) {
                provinceTypeCountMap.put(typeEnum.getCode(), 0);
                cityTypeCountMap.put(typeEnum.getCode(), 0);
            }

            // 根据查询结果更新计数
            for (LawPosition position : allPositions) {
                if ("1".equals(position.getPositionLevel())) {
                    // 省级数据
                    String typeCode = position.getPositionType();
                    provinceTypeCountMap.put(typeCode, provinceTypeCountMap.getOrDefault(typeCode, 0) + 1);
                } else if ("2".equals(position.getPositionLevel())) {
                    // 市级数据
                    String typeCode = position.getPositionType();
                    cityTypeCountMap.put(typeCode, cityTypeCountMap.getOrDefault(typeCode, 0) + 1);
                }
            }

            // 4. 构建结果
            List<JSONObject> provinceList = new ArrayList<>();
            List<JSONObject> cityList = new ArrayList<>();

            for (LawPositionBaseEnum lawPositionTypeEnum : LawPositionBaseEnum.values()) {
                String code = lawPositionTypeEnum.getCode();

                // 省级数据
                JSONObject provinceTypeObj = new JSONObject();
                provinceTypeObj.put("name", lawPositionTypeEnum.getDescription());
                provinceTypeObj.put("code", code);
                provinceTypeObj.put("value", provinceTypeCountMap.get(code));
                provinceList.add(provinceTypeObj);

                // 市级数据
                JSONObject cityTypeObj = new JSONObject();
                cityTypeObj.put("name", lawPositionTypeEnum.getDescription());
                cityTypeObj.put("code", code);
                cityTypeObj.put("value", cityTypeCountMap.get(code));
                cityList.add(cityTypeObj);
            }

            provinceMap.put("provinceList", provinceList);
            cityMap.put("cityList", cityList);

            return map;
        }

        return Collections.emptyMap();
    }

    @Override
    public List<JSONObject> cultureDetail(String areaName, Integer type) {
        List<JSONObject> jsonObjectList = new ArrayList<>();
        //省级查看地市的数量
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            CommonConstant.CITY_LIST.forEach(city -> {
                String areaCodeByName = getAreaCodeByName(city);
                Map<String, Object> cityMap = new HashMap<>();
                QueryWrapper<LawPosition> cityQueryWrapper = new QueryWrapper<>();
                if (type != null) {
                    cityQueryWrapper.lambda().eq(LawPosition::getPositionType, type);
                } else {
                    cityQueryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
                }
                cityQueryWrapper.lambda().eq(LawPosition::getPositionLevel, 1).like(LawPosition::getAddressCode, areaCodeByName);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("total", lawPositionService.count(cityQueryWrapper));
                jsonObjectList.add(jsonObject);
            });
        } else {
            //区县直接查列表
            String areaCode = getAreaCodeByName(areaName);
            QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
            if (type != null) {
                queryWrapper.lambda().eq(LawPosition::getPositionType, type);
            } else {
                queryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
            }
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, 2).like(LawPosition::getAddressCode, areaCode);
            List<LawPosition> lawPositions = lawPositionService.list(queryWrapper);
            if (ObjectUtil.isNotEmpty(lawPositions)) {
                for (LawPosition lawPosition : lawPositions) {
                    // 先将对象转换为JSONObject
                    JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                    jsonObjectList.add(jsonObject);
                }
                return jsonObjectList;
            }
        }
        return jsonObjectList;
    }

    @Override
    public List<JSONObject> cultureSearch(String areaName, String name, Integer type, Integer positionLevel) {
        String areaCodeByName = getAreaCodeByName(areaName);
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        if (type != null) {
            queryWrapper.lambda().eq(LawPosition::getPositionType, type);
        } else {
            queryWrapper.lambda().in(LawPosition::getPositionType, 7, 8, 9, 10, 11, 12);
        }
        if (positionLevel != null) {
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, positionLevel);
        }
        if (ObjectUtil.isNotEmpty(name)) {
            queryWrapper.lambda().like(LawPosition::getPositionName, name);
        }
        queryWrapper.lambda().like(LawPosition::getAddressCode, areaCodeByName);
        List<LawPosition> lawPositions = lawPositionService.list(queryWrapper);
        if (ObjectUtil.isNotEmpty(lawPositions)) {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (LawPosition lawPosition : lawPositions) {
                // 先将对象转换为JSONObject
                JSONObject jsonObject = (JSONObject) JSONObject.toJSON(lawPosition);
                jsonObjectList.add(jsonObject);
            }
            return jsonObjectList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<JSONObject> lawVisit(String areaName, Integer year) {
        List<JSONObject> resultList = new ArrayList<>();

        // 获取当前日期并设置日期格式
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");

        // 确定查询的年份
        int targetYear = (year != null) ? year : calendar.get(Calendar.YEAR);

        // 查询指定年份的12个月访问数据
        for (int month = 1; month <= 12; month++) {
            JSONObject data = new JSONObject();

            // 设置月份
            calendar.set(targetYear, month - 1, 1, 0, 0, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();

            // 设置月末
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.SECOND, -1);
            Date endDate = calendar.getTime();

            String monthStr = String.format("%d-%02d", targetYear, month);

            // 构建查询条件
            LambdaQueryWrapper<SysVisLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SysVisLog::getRemark, "zlb")
                    .between(SysVisLog::getVisTime, startDate, endDate);

            // 添加地区筛选
            if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
                // 获取区域代码
                String areaCode = getAreaCodeByName(areaName);

                if (StringUtils.isNotBlank(areaCode)) {
                    // 查询属于指定地区的用户账号
                    QueryWrapper<SysUser> userQuery = new QueryWrapper<>();

                    // 根据地区类型进行筛选
                    if (CommonConstant.CITY_LIST.contains(areaName)) {
                        // 如果是地市，使用cityCode筛选
                        userQuery.lambda().eq(SysUser::getCityCode, areaCode);
                    } else {
                        // 如果是区县，使用areaCode筛选
                        userQuery.lambda().eq(SysUser::getAreaCode, areaCode);
                    }

                    List<SysUser> users = sysUserService.list(userQuery);

                    if (CollectionUtil.isNotEmpty(users)) {
                        List<String> accounts = users.stream()
                                .map(SysUser::getAccount)
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList());

                        if (CollectionUtil.isNotEmpty(accounts)) {
                            queryWrapper.in(SysVisLog::getAccount, accounts);
                        } else {
                            // 如果没有找到对应的用户账号，返回空结果
                            queryWrapper.eq(SysVisLog::getId, -1);
                        }
                    } else {
                        // 如果没有找到对应的用户，返回空结果
                        queryWrapper.eq(SysVisLog::getId, -1);
                    }
                }
            }

            int visits = sysVisLogService.count(queryWrapper);

            data.put("month", monthStr);
            data.put("visits", visits);
            resultList.add(data);
        }

        return resultList;
    }

    /**
     * 普法学习统计
     * 修改说明：
     * 1. 数据来源变更：从 LawEducationResourcesView 表统计改为直接从 LeResources 表获取统计数据
     * 2. 区域筛选变更：从基于用户地区代码筛选改为基于资源单位（deptId）筛选
     * 3. 统计字段变更：观看数使用 LeResources.viewNum，收藏数使用 LeResources.starNum
     * 4. 保持返回结构不变：返回的 JSON 结构与原方法完全一致
     *
     * @param areaName 地区名称
     * @return 包含视频和文章统计数据的Map
     */
    @Override
    public Map<String, Object> lawLearn(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取指定地区的单位ID列表（基于资源单位进行区域筛选）
        Set<String> areaDeptIds = new HashSet<>();
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 根据地区名称获取对应的组织机构ID
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                // 获取该组织机构及其下级机构的所有ID
                areaDeptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 统计普法视频的点赞、观看和收藏数据
        JSONObject videoData = new JSONObject();
        videoData.put("name", "普法视频");

        // 查询视频资源（type以3或4开头的是视频资源）
        QueryWrapper<LeResources> videoQueryWrapper = new QueryWrapper<>();
        videoQueryWrapper.lambda().and(q -> q
                .likeRight(LeResources::getType, "3")
                .or()
                .likeRight(LeResources::getType, "4"));
        videoQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源

        // 根据地区筛选资源单位
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            if (CollectionUtil.isNotEmpty(areaDeptIds)) {
                videoQueryWrapper.lambda().in(LeResources::getDeptId, areaDeptIds);
            } else {
                // 如果指定了地区但没有找到对应的单位，则返回空结果
                videoData.put("点赞", 0);
                videoData.put("观看", 0);
                videoData.put("收藏", 0);

                // 统计普法文章数据（同样返回空结果）
                JSONObject articleData = new JSONObject();
                articleData.put("name", "普法文章");
                articleData.put("点赞", 0);
                articleData.put("观看", 0);
                articleData.put("收藏", 0);

                List<JSONObject> resourceData = new ArrayList<>();
                resourceData.add(videoData);
                resourceData.add(articleData);
                resultMap.put("resourceData", resourceData);
                resultMap.put("totalLearners", 0);
                return resultMap;
            }
        }

        List<LeResources> videoResources = leResourcesService.list(videoQueryWrapper);

        // 视频点赞数（直接从LeResources表的likesNum字段统计）
        int videoLikes = videoResources.stream()
                .mapToInt(resource -> resource.getLikesNum() != null ? resource.getLikesNum() : 0)
                .sum();
        videoData.put("点赞", videoLikes);

        // 视频观看数（直接从LeResources表的viewNum字段统计）
        int videoViews = videoResources.stream()
                .mapToInt(resource -> resource.getViewNum() != null ? resource.getViewNum() : 0)
                .sum();
        videoData.put("观看", videoViews);

        // 视频收藏数（直接从LeResources表的starNum字段统计）
        int videoStars = videoResources.stream()
                .mapToInt(resource -> resource.getStarNum() != null ? resource.getStarNum() : 0)
                .sum();
        videoData.put("收藏", videoStars);

        // 统计普法文章的点赞、观看和收藏数据
        JSONObject articleData = new JSONObject();
        articleData.put("name", "普法文章");

        // 查询文章资源（type以1或2开头的是文章/图文资源）
        QueryWrapper<LeResources> articleQueryWrapper = new QueryWrapper<>();
        articleQueryWrapper.lambda().and(q -> q
                .likeRight(LeResources::getType, "1")
                .or()
                .likeRight(LeResources::getType, "2"));
        articleQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源

        // 根据地区筛选资源单位
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            if (CollectionUtil.isNotEmpty(areaDeptIds)) {
                articleQueryWrapper.lambda().in(LeResources::getDeptId, areaDeptIds);
            } else {
                // 如果指定了地区但没有找到对应的单位，则返回空结果
                articleData.put("点赞", 0);
                articleData.put("观看", 0);
                articleData.put("收藏", 0);

                List<JSONObject> resourceData = new ArrayList<>();
                resourceData.add(videoData);
                resourceData.add(articleData);
                resultMap.put("resourceData", resourceData);
                resultMap.put("totalLearners", 0);
                return resultMap;
            }
        }

        List<LeResources> articleResources = leResourcesService.list(articleQueryWrapper);

        // 文章点赞数（直接从LeResources表的likesNum字段统计）
        int articleLikes = articleResources.stream()
                .mapToInt(resource -> resource.getLikesNum() != null ? resource.getLikesNum() : 0)
                .sum();
        articleData.put("点赞", articleLikes);

        // 文章观看数（直接从LeResources表的viewNum字段统计）
        int articleViews = articleResources.stream()
                .mapToInt(resource -> resource.getViewNum() != null ? resource.getViewNum() : 0)
                .sum();
        articleData.put("观看", articleViews);

        // 文章收藏数（直接从LeResources表的starNum字段统计）
        int articleStars = articleResources.stream()
                .mapToInt(resource -> resource.getStarNum() != null ? resource.getStarNum() : 0)
                .sum();
        articleData.put("收藏", articleStars);

        // 将视频和文章数据添加到结果中
        List<JSONObject> resourceData = new ArrayList<>();
        resourceData.add(videoData);
        resourceData.add(articleData);
        resultMap.put("resourceData", resourceData);

        // 总学法人数（视频观看数 + 文章观看数）
        int totalLearners = videoViews + articleViews;
        resultMap.put("totalLearners", totalLearners);

        return resultMap;
    }

    @Override
    public Map<String, Object> lawTest(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 查询测评数据 - 公民法治素养测评和公民法治素养基准点自测
        // 1. 公民法治素养测评次数
        int examTestCount = 0;
        // 2. 公民法治素养基准点自测次数
        int benchmarkTestCount = 0;

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 全省统计
            examTestCount = bigScreenMapper.examTestCount(null);
            benchmarkTestCount = bigScreenMapper.benchmarkTestCount(null);
        } else {
            // 获取区域编码
            String areaCode = getAreaCodeByName(areaName);

            if (ObjectUtil.isNotEmpty(areaCode)) {
                // 获取包含本级和下级的所有区域代码
                Set<String> areaCodes = cityAreaInfoService.getAreaCodes(areaCode);

                if (CollectionUtil.isNotEmpty(areaCodes)) {
                    // 对于 benchmark_test_result 表，直接使用区域代码查询
                    benchmarkTestCount = bigScreenMapper.benchmarkTestCountByAreaCodes(areaCodes);

                    // 对于 exam_result 表，需要先获取区域名称，然后查询
                    // 因为 exam_result 表没有 area_code 字段，只有 city 和 area 字段存储中文名称
                    List<String> areaNamesList = cityAreaInfoMapper.getAreaNamesByIds(areaCodes);
                    if (CollectionUtil.isNotEmpty(areaNamesList)) {
                        Set<String> areaNames = new HashSet<>(areaNamesList);
                        examTestCount = bigScreenMapper.examTestCountByAreaNames(areaNames);
                    }
                } else {
                    // 如果没有找到下级区域，使用原来的模糊查询方式
                    examTestCount = bigScreenMapper.examTestCount(areaCode);
                    benchmarkTestCount = bigScreenMapper.benchmarkTestCount(areaCode);
                }
            }
        }

        // 添加两种测评数据
        resultMap.put("examTestCount", examTestCount);
        resultMap.put("benchmarkTestCount", benchmarkTestCount);

        return resultMap;
    }

    @Override
    public Map<String, Object> lawNews(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 查询所有父标签
        QueryWrapper<LerLabel> parentLabelQuery = new QueryWrapper<>();
        parentLabelQuery.lambda().isNull(LerLabel::getPid);
        parentLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
        List<LerLabel> parentLabels = lerLabelService.list(parentLabelQuery);

        // 创建父标签统计列表
        List<JSONObject> parentLabelStats = new ArrayList<>();
        // 总数统计
        int totalCount = 0;

        for (LerLabel parentLabel : parentLabels) {
            // 查询所有子标签
            QueryWrapper<LerLabel> childLabelQuery = new QueryWrapper<>();
            childLabelQuery.lambda().eq(LerLabel::getPid, parentLabel.getId());
            childLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
            List<LerLabel> childLabels = lerLabelService.list(childLabelQuery);

            // 查询父标签关联的所有资源数量
            List<String> labelIdList = new ArrayList<>();
            labelIdList.add(parentLabel.getId());

            // 子标签ID列表
            if (CollectionUtil.isNotEmpty(childLabels)) {
                childLabels.forEach(childLabel -> labelIdList.add(childLabel.getId()));
            }

            // 统计标签关联的资源总数，使用Set去重避免重复计算
            Set<String> allResourceIds = new HashSet<>();
            if (CollectionUtil.isNotEmpty(labelIdList)) {
                for (String labelId : labelIdList) {
                    List<String> resourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(labelId);
                    if (CollectionUtil.isNotEmpty(resourceIds)) {
                        allResourceIds.addAll(resourceIds); // 添加到Set中自动去重
                    }
                }
            }

            int count = 0;
            if (CollectionUtil.isNotEmpty(allResourceIds)) {
                // 按区域过滤资源
                if (ObjectUtil.isNotEmpty(deptIds)) {
                    // 查询这些资源中属于指定区域的数量
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, allResourceIds)
                            .eq(LeResources::getSource, 0)
                            .in(LeResources::getDeptId, deptIds)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                } else {
                    // 不需要按区域过滤，但需要过滤状态
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, allResourceIds)
                            .eq(LeResources::getSource, 0)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                }
            }

            // 添加到统计结果中
            JSONObject parentStat = new JSONObject();
            parentStat.put("name", parentLabel.getName());
            parentStat.put("value", count);
            parentLabelStats.add(parentStat);

            totalCount += count;
        }

        // 查询所有子标签统计数据
        List<JSONObject> childLabelStats = new ArrayList<>();

        // 针对每个父标签，查询其下的子标签统计
        for (LerLabel parentLabel : parentLabels) {
            QueryWrapper<LerLabel> childLabelQuery = new QueryWrapper<>();
            childLabelQuery.lambda().eq(LerLabel::getPid, parentLabel.getId());
            childLabelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
            List<LerLabel> childLabels = lerLabelService.list(childLabelQuery);

            for (LerLabel childLabel : childLabels) {
                List<String> resourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(childLabel.getId());

                // 按区域过滤资源
                int count = 0;
                if (ObjectUtil.isNotEmpty(deptIds) && CollectionUtil.isNotEmpty(resourceIds)) {
                    // 查询这些资源中属于指定区域的数量
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, resourceIds)
                            .eq(LeResources::getSource, 0) // 添加source过滤条件，保持与父标签统计一致
                            .in(LeResources::getDeptId, deptIds)
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                } else if (CollectionUtil.isNotEmpty(resourceIds)) {
                    // 不需要按区域过滤，但需要过滤状态
                    QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                    resourceQuery.lambda()
                            .in(LeResources::getId, resourceIds)
                            .eq(LeResources::getSource, 0) // 添加source过滤条件，保持与父标签统计一致
                            .in(LeResources::getStatus, 1, 2, 5, 6); // 只统计有效资源
                    count = leResourcesService.count(resourceQuery);
                }

                if (count > 0) {  // 只显示有资源的标签
                    JSONObject childStat = new JSONObject();
                    childStat.put("name", childLabel.getName());
                    childStat.put("value", count);
                    childStat.put("parentName", parentLabel.getName());
                    childLabelStats.add(childStat);
                }
            }
        }

        // 将结果放入返回Map
        resultMap.put("totalCount", totalCount);
        resultMap.put("parentStats", parentLabelStats);
        resultMap.put("childStats", childLabelStats);

        return resultMap;
    }

    @Override
    public Map<String, Object> lawReportByArea(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 构建基本查询条件
        QueryWrapper<LeResources> baseQuery = new QueryWrapper<>();
        baseQuery.lambda().in(LeResources::getStatus, 1, 2, 5, 6) // 只统计有效资源
                .eq(LeResources::getSource, "0"); // 只统计source_='0'的资源，与lawReport方法保持一致

        // 按区域过滤
        if (ObjectUtil.isNotEmpty(deptIds)) {
            baseQuery.lambda().in(LeResources::getDeptId, deptIds);
        }

        // 1. 统计该区域资源总数
        int totalResources = leResourcesService.count(baseQuery);
        resultMap.put("totalResources", totalResources);

        // 2. 统计本周资源上报数量
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date weekStart = calendar.getTime();

        QueryWrapper<LeResources> weekQuery = new QueryWrapper<>();
        weekQuery.lambda().in(LeResources::getStatus, 1, 2, 5, 6) // 只统计有效资源
                .eq(LeResources::getSource, "0"); // 只统计source_='0'的资源，与lawReport方法保持一致
        // 按区域过滤
        if (ObjectUtil.isNotEmpty(deptIds)) {
            weekQuery.lambda().in(LeResources::getDeptId, deptIds);
        }
        weekQuery.lambda().ge(LeResources::getCreateTime, weekStart);
        int weeklyResources = leResourcesService.count(weekQuery);
        resultMap.put("weeklyResources", weeklyResources);

        // 3. 近期最热门的三个标签
        // 先找出区域内所有资源的ID
        List<LeResources> resources = leResourcesService.list(baseQuery);
        List<String> resourceIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resources)) {
            resourceIds = resources.stream().map(LeResources::getId).collect(Collectors.toList());
        }

        // 统计各标签关联的资源数量
        List<JSONObject> hotLabels = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(resourceIds)) {
            // 查询所有有效标签
            QueryWrapper<LerLabel> labelQuery = new QueryWrapper<>();
            labelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
            List<LerLabel> allLabels = lerLabelService.list(labelQuery);

            // 统计每个标签关联的资源数量
            for (LerLabel label : allLabels) {
                List<String> labelResourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(label.getId());
                if (CollectionUtil.isNotEmpty(labelResourceIds)) {
                    // 计算交集，找出既属于该区域又关联此标签的资源
                    labelResourceIds.retainAll(resourceIds);
                    int count = labelResourceIds.size();

                    if (count > 0) {
                        JSONObject labelInfo = new JSONObject();
                        labelInfo.put("name", label.getName());
                        labelInfo.put("value", count);
                        hotLabels.add(labelInfo);
                    }
                }
            }

            // 按资源数量降序排序
            hotLabels.sort((o1, o2) -> o2.getInteger("value").compareTo(o1.getInteger("value")));

            // 取前三个
            if (hotLabels.size() > 3) {
                hotLabels = hotLabels.subList(0, 3);
            }
        }

        // 将热门标签拼接成字符串
        StringBuilder hotLabelStr = new StringBuilder("近期热门：");
        if (CollectionUtil.isNotEmpty(hotLabels)) {
            for (int i = 0; i < hotLabels.size(); i++) {
                if (i > 0) {
                    hotLabelStr.append("、");
                }
                hotLabelStr.append(hotLabels.get(i).getString("name"));
            }
        } else {
            hotLabelStr.append("暂无数据");
        }
        resultMap.put("hotLabels", hotLabelStr.toString());

        // 添加区域信息
        resultMap.put("areaName", areaName);
        resultMap.put("reportCount", totalResources);

        return resultMap;
    }

    @Override
    public List<JSONObject> lawCulture(String areaName) {
        // 获取法治文化阵地数据，包括不同类型的法治文化阵地总数
        List<JSONObject> resultList = new ArrayList<>();

        // 全国阵地总数（阵地级别为1的）
        QueryWrapper<LawPosition> nationalWrapper = new QueryWrapper<>();
        nationalWrapper.lambda().eq(LawPosition::getPositionLevel, "1");
        int nationalCount = lawPositionService.count(nationalWrapper);

        // 省级阵地总数（阵地级别为2的）
        QueryWrapper<LawPosition> provinceWrapper = new QueryWrapper<>();
        provinceWrapper.lambda().eq(LawPosition::getPositionLevel, "2");
        int provinceCount = lawPositionService.count(provinceWrapper);

        // 总数
        int total = lawPositionService.count();

        // 统计各类型法治文化阵地数量
        // 中华优秀传统法律文化阵地(7)、红色法治文化阵地(8)、宪法宣传教育阵地(9)、
        // 民法典宣传教育阵地(10)、青少年法治宣传教育阵地(11)、其他地方特色法治文化阵地(12)
        Map<String, Object> countMap = new HashMap<>();
        countMap.put("国", nationalCount);
        countMap.put("省", provinceCount);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("total", total);
        jsonObject.put("nationalCount", nationalCount);
        jsonObject.put("provinceCount", provinceCount);

        // 统计各类型法治文化阵地数量
        for (LawPositionBaseEnum type : LawPositionBaseEnum.values()) {
            QueryWrapper<LawPosition> typeWrapper = new QueryWrapper<>();
            typeWrapper.lambda().eq(LawPosition::getPositionType, type.getCode());

            // 如果指定了区域名称，进行区域过滤
            if (StringUtils.isNotBlank(areaName)) {
                typeWrapper.lambda().like(LawPosition::getAddressName, areaName);
            }

            int count = lawPositionService.count(typeWrapper);

            JSONObject typeJson = new JSONObject();
            typeJson.put("name", type.getDescription());
            typeJson.put("value", count);
            typeJson.put("code", type.getCode());
            resultList.add(typeJson);
        }

        return resultList;
    }

    @Override
    public List<JSONObject> lawCultureByArea(String areaName) {
        // 按地区统计法治文化阵地分布情况
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有城市
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT city");
        if (StringUtils.isNotBlank(areaName)) {
            queryWrapper.lambda().like(LawPosition::getAddressName, areaName);
        }
        List<LawPosition> cityList = lawPositionService.list(queryWrapper);

        // 对每个城市统计全国级和省级阵地数量
        for (LawPosition position : cityList) {
            String city = position.getCity();
            if (StringUtils.isBlank(city)) {
                continue;
            }

            JSONObject cityJson = new JSONObject();
            cityJson.put("name", city);

            // 统计全国级阵地数量
            QueryWrapper<LawPosition> nationalWrapper = new QueryWrapper<>();
            nationalWrapper.lambda().eq(LawPosition::getCity, city)
                    .eq(LawPosition::getPositionLevel, "1");
            int nationalCount = lawPositionService.count(nationalWrapper);
            cityJson.put("national", nationalCount);

            // 统计省级阵地数量
            QueryWrapper<LawPosition> provinceWrapper = new QueryWrapper<>();
            provinceWrapper.lambda().eq(LawPosition::getCity, city)
                    .eq(LawPosition::getPositionLevel, "2");
            int provinceCount = lawPositionService.count(provinceWrapper);
            cityJson.put("province", provinceCount);

            // 统计总数
            cityJson.put("total", nationalCount + provinceCount);

            resultList.add(cityJson);
        }

        // 按总数排序
        resultList.sort((o1, o2) -> {
            Integer total1 = o1.getInteger("total");
            Integer total2 = o2.getInteger("total");
            return total2.compareTo(total1);
        });

        return resultList;
    }

    @Override
    public List<JSONObject> lawCultureList(String areaName) {
        // 获取法治文化阵地列表数据
        List<JSONObject> resultList = new ArrayList<>();

        // 查询法治文化阵地
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByDesc(LawPosition::getCreateTime);

        // 只查询法治文化阵地相关类型
        queryWrapper.lambda().in(LawPosition::getPositionType,
                LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(),
                LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(),
                LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(),
                LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());

        // 如果指定了区域名称，进行区域过滤
        if (StringUtils.isNotBlank(areaName)) {
            queryWrapper.lambda().like(LawPosition::getAddressName, areaName);
        }

        // 限制返回数量
        Page<LawPosition> page = new Page<>(1, 10);
        Page<LawPosition> positionPage = lawPositionService.page(page, queryWrapper);

        // 转换为JSONObject列表
        for (LawPosition position : positionPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", position.getId());
            jsonObject.put("name", position.getPositionName());
            jsonObject.put("address", position.getAddressDetail());
            jsonObject.put("type", position.getPositionType());

            // 获取类型名称
            LawPositionBaseEnum typeEnum = LawPositionBaseEnum.getByCode(position.getPositionType());
            if (typeEnum != null) {
                jsonObject.put("typeName", typeEnum.getDescription());
            }

            // 获取级别名称
            String levelName = "未知";
            if ("1".equals(position.getPositionLevel())) {
                levelName = "全国";
            } else if ("2".equals(position.getPositionLevel())) {
                levelName = "省级";
            }
            jsonObject.put("level", position.getPositionLevel());
            jsonObject.put("levelName", levelName);

            resultList.add(jsonObject);
        }

        return resultList;
    }

    @Override
    public List<JSONObject> lawActivityTotal(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 查询各地市的法律活动数量统计
        String areaCode = null;
        if (StringUtils.isNotBlank(areaName)) {
            areaCode = getAreaCodeByName(areaName);
        }

        // 通过SQL查询获取各地市的活动统计数据
        List<Map<String, Object>> cityActivityStats = bigScreenMapper.getCityActivityStatistics(areaCode);

        // 处理查询结果，转换为前端需要的格式
        if (CollectionUtil.isNotEmpty(cityActivityStats)) {
            for (Map<String, Object> stat : cityActivityStats) {
                JSONObject json = new JSONObject();
                String cityName = (String) stat.get("city_name");
                Integer count = ((Number) stat.get("activity_count")).intValue();

                json.put("name", cityName);
                json.put("value", count);
                resultList.add(json);
            }
        } else {
            for (String s : CommonConstant.CITY_LIST) {
                JSONObject json = new JSONObject();
                json.put("name", s);
                json.put("value", 0);
                resultList.add(json);
            }
        }

        return resultList;
    }

    @Override
    public List<JSONObject> lawArt(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 漫画 - 11
        JSONObject manhuaJson = new JSONObject();
        manhuaJson.put("name", "漫画");
        manhuaJson.put("value", countResourcesByTypeAndArea("11", areaName));
        manhuaJson.put("code", "11");
        resultList.add(manhuaJson);

        // 宣传画 - 12
        JSONObject xuanchuanhuaJson = new JSONObject();
        xuanchuanhuaJson.put("name", "宣传画");
        xuanchuanhuaJson.put("value", countResourcesByTypeAndArea("12", areaName));
        xuanchuanhuaJson.put("code", "12");
        resultList.add(xuanchuanhuaJson);

        // 海报 - 13
        JSONObject haibaoJson = new JSONObject();
        haibaoJson.put("name", "海报");
        haibaoJson.put("value", countResourcesByTypeAndArea("13", areaName));
        haibaoJson.put("code", "13");
        resultList.add(haibaoJson);

        // 动画 - 31
        JSONObject donghuaJson = new JSONObject();
        donghuaJson.put("name", "动画");
        donghuaJson.put("value", countResourcesByTypeAndArea("31", areaName));
        donghuaJson.put("code", "31");
        resultList.add(donghuaJson);

        // 宣传片 - 32
        JSONObject xuanchuanpianJson = new JSONObject();
        xuanchuanpianJson.put("name", "宣传片");
        xuanchuanpianJson.put("value", countResourcesByTypeAndArea("32", areaName));
        xuanchuanpianJson.put("code", "32");
        resultList.add(xuanchuanpianJson);

        // 微电影 - 33
        JSONObject weidiangyingJson = new JSONObject();
        weidiangyingJson.put("name", "微电影");
        weidiangyingJson.put("value", countResourcesByTypeAndArea("33", areaName));
        weidiangyingJson.put("code", "33");
        resultList.add(weidiangyingJson);

        // 情景剧 - 34
        JSONObject qingjingjuJson = new JSONObject();
        qingjingjuJson.put("name", "情景剧");
        qingjingjuJson.put("value", countResourcesByTypeAndArea("34", areaName));
        qingjingjuJson.put("code", "34");
        resultList.add(qingjingjuJson);

        // 视频课件 - 41
        JSONObject videoJson = new JSONObject();
        videoJson.put("name", "视频课件");
        videoJson.put("value", countResourcesByTypeAndArea("41", areaName));
        videoJson.put("code", "41");
        resultList.add(videoJson);

        // 文档课件 - 42
        JSONObject docJson = new JSONObject();
        docJson.put("name", "文档课件");
        docJson.put("value", countResourcesByTypeAndArea("42", areaName));
        docJson.put("code", "42");
        resultList.add(docJson);

        // 公益广告 - 1
        JSONObject adJson = new JSONObject();
        adJson.put("name", "公益广告");
        adJson.put("value", countResourcesByTypeAndArea("1", areaName));
        adJson.put("code", "1");
        resultList.add(adJson);

        // 计算所有文艺库资源总数
        int total = resultList.stream().mapToInt(item -> item.getInteger("value")).sum();

        // 添加总计数据
        JSONObject totalJson = new JSONObject();
        totalJson.put("name", "法治文艺");
        totalJson.put("value", total);
        totalJson.put("code", "total");
        resultList.add(0, totalJson);

        return resultList;
    }

    @Override
    public List<JSONObject> lawArtCategory(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有有效的资源，按主题分类统计
        QueryWrapper<LeResources> baseQueryWrapper = new QueryWrapper<>();

        // 有效状态的资源
        baseQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        // 只统计普法资源库的数据（source = "1"）
        baseQueryWrapper.lambda().eq(LeResources::getSource, "0");

        // 区域筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            baseQueryWrapper.lambda().like(LeResources::getDeptName, areaName);
        }

        // 获取所有符合条件的资源
        List<LeResources> allResources = leResourcesService.list(baseQueryWrapper);

        // 按主题分类统计（支持多选）
        Map<String, Integer> themeCountMap = new HashMap<>();

        for (LeResources resource : allResources) {
            String themeType = resource.getThemeType();

            // 如果主题分类为空，归类为"其他"
            if (StringUtils.isBlank(themeType)) {
                themeCountMap.put("其他", themeCountMap.getOrDefault("其他", 0) + 1);
            } else {
                // 处理多选情况，按逗号分隔
                String[] themeTypes = themeType.split(",");
                for (String singleTheme : themeTypes) {
                    String trimmedTheme = singleTheme.trim();
                    if (StringUtils.isNotBlank(trimmedTheme)) {
                        themeCountMap.put(trimmedTheme, themeCountMap.getOrDefault(trimmedTheme, 0) + 1);
                    }
                }
            }
        }

        // 获取字典值对应的显示名称（如果有pfzy_ztfl字典的话）
        for (Map.Entry<String, Integer> entry : themeCountMap.entrySet()) {
            JSONObject themeJson = new JSONObject();
            String themeCode = entry.getKey();
            Integer count = entry.getValue();

            // 尝试获取字典显示名称，如果获取失败则使用原始值
            String themeName = getDictDisplayName("pfzy_ztfl", themeCode);
            if (StringUtils.isBlank(themeName)) {
                themeName = themeCode;
            }

            themeJson.put("name", themeName);
            themeJson.put("value", count);
            resultList.add(themeJson);
        }

        // 按数量从大到小排序
        resultList.sort((o1, o2) -> o2.getInteger("value").compareTo(o1.getInteger("value")));

        return resultList;
    }

    /**
     * 获取字典显示名称
     * @param dictTypeCode 字典类型编码
     * @param dictCode 字典编码
     * @return 字典显示名称
     */
    private String getDictDisplayName(String dictTypeCode, String dictCode) {
        try {
            if (StringUtils.isBlank(dictCode)) {
                return "";
            }

            // 查询字典类型
            QueryWrapper<SysDictType> typeQueryWrapper = new QueryWrapper<>();
            typeQueryWrapper.lambda().eq(SysDictType::getCode, dictTypeCode)
                    .eq(SysDictType::getStatus, 0);
            SysDictType dictType = sysDictTypeService.getOne(typeQueryWrapper);

            if (dictType == null) {
                return "";
            }

            // 查询字典数据
            QueryWrapper<SysDictData> dataQueryWrapper = new QueryWrapper<>();
            dataQueryWrapper.lambda().eq(SysDictData::getTypeId, dictType.getId())
                    .eq(SysDictData::getCode, dictCode)
                    .eq(SysDictData::getStatus, 0);
            SysDictData dictData = sysDictDataService.getOne(dataQueryWrapper);

            return dictData != null ? dictData.getValue() : "";
        } catch (Exception e) {
            log.warn("获取字典显示名称失败: dictTypeCode={}, dictCode={}", dictTypeCode, dictCode, e);
            return "";
        }
    }

    @Override
    public List<JSONObject> lawArtGroup(String areaName) {
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有有效的资源，按面向群体分类统计
        QueryWrapper<LeResources> baseQueryWrapper = new QueryWrapper<>();

        // 有效状态的资源
        baseQueryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        // 只统计普法资源库的数据（source = "1"）
        baseQueryWrapper.lambda().eq(LeResources::getSource, "1");

        // 区域筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            baseQueryWrapper.lambda().like(LeResources::getDeptName, areaName);
        }

        // 获取所有符合条件的资源
        List<LeResources> allResources = leResourcesService.list(baseQueryWrapper);

        // 按面向群体分类统计（支持多选）
        Map<String, Integer> audienceCountMap = new HashMap<>();

        for (LeResources resource : allResources) {
            String audienceType = resource.getAudienceType();

            // 如果面向群体分类为空，归类为"其他"
            if (StringUtils.isBlank(audienceType)) {
                audienceCountMap.put("其他", audienceCountMap.getOrDefault("其他", 0) + 1);
            } else {
                // 处理多选情况，按逗号分隔
                String[] audienceTypes = audienceType.split(",");
                for (String singleAudience : audienceTypes) {
                    String trimmedAudience = singleAudience.trim();
                    if (StringUtils.isNotBlank(trimmedAudience)) {
                        audienceCountMap.put(trimmedAudience, audienceCountMap.getOrDefault(trimmedAudience, 0) + 1);
                    }
                }
            }
        }

        // 获取字典值对应的显示名称（如果有pfzy-mxrq字典的话）
        for (Map.Entry<String, Integer> entry : audienceCountMap.entrySet()) {
            JSONObject groupJson = new JSONObject();
            String audienceCode = entry.getKey();
            Integer count = entry.getValue();

            // 尝试获取字典显示名称，如果获取失败则使用原始值
            String audienceName = getDictDisplayName("pfzy-mxrq", audienceCode);
            if (StringUtils.isBlank(audienceName)) {
                audienceName = audienceCode;
            }

            groupJson.put("name", audienceName);
            groupJson.put("value", count);
            resultList.add(groupJson);
        }

        // 过滤掉数量为0的群体
        resultList = resultList.stream()
                .filter(json -> json.getInteger("value") > 0)
                .collect(Collectors.toList());

        // 按数量从大到小排序
        resultList.sort((o1, o2) -> o2.getInteger("value").compareTo(o1.getInteger("value")));

        return resultList;
    }

    /**
     * 根据资源类型和区域统计资源数量
     *
     * @param type     资源类型
     * @param areaName 区域名称
     * @return 资源数量
     */
    private int countResourcesByTypeAndArea(String type, String areaName) {
        QueryWrapper<LeResources> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LeResources::getType, type);

        // 区域筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            queryWrapper.lambda().like(LeResources::getDeptName, areaName);
        }

        // 有效状态的资源 (1已通过,2已通过且被合并,5省市直接新增,6省厅采用)
        queryWrapper.lambda().in(LeResources::getStatus, 1, 2, 5, 6);

        return leResourcesService.count(queryWrapper);
    }

    @Override
    public Map<String, Object> lawHot(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();

        // 查询所有已审核的标签
        QueryWrapper<LerLabel> labelQuery = new QueryWrapper<>();
        labelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
        List<LerLabel> allLabels = lerLabelService.list(labelQuery);

        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 统计每个标签关联的资源数量
        List<JSONObject> labelStats = new ArrayList<>();
        for (LerLabel label : allLabels) {
            // 获取标签关联的所有资源ID
            List<String> resourceIds = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(label.getId());

            // 按区域过滤资源
            if (ObjectUtil.isNotEmpty(deptIds) && CollectionUtil.isNotEmpty(resourceIds)) {
                // 查询这些资源中属于指定区域的数量
                QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
                resourceQuery.lambda()
                        .in(LeResources::getId, resourceIds)
                        .in(LeResources::getDeptId, deptIds);
                int count = leResourcesService.count(resourceQuery);

                // 只统计有资源关联的标签
                if (count > 0) {
                    JSONObject labelStat = new JSONObject();
                    labelStat.put("name", label.getName());
                    labelStat.put("value", count);
                    labelStats.add(labelStat);
                }
            } else {
                // 不需要按区域过滤，直接统计所有资源
                int count = CollectionUtil.isEmpty(resourceIds) ? 0 : resourceIds.size();

                // 只统计有资源关联的标签
                if (count > 0) {
                    JSONObject labelStat = new JSONObject();
                    labelStat.put("name", label.getName());
                    labelStat.put("value", count);
                    labelStats.add(labelStat);
                }
            }
        }

        // 按资源数量升序排序（数量越多排名越后）
        labelStats.sort(Comparator.comparingInt(o -> o.getInteger("value")));

        // 构建排名数据
        List<JSONObject> rankList = new ArrayList<>();
        int rank = 1;
        int maxRank = Math.min(7, labelStats.size()); // 最多取前7个

        for (int i = 0; i < maxRank; i++) {
            JSONObject item = labelStats.get(i);
            JSONObject rankItem = new JSONObject();
            rankItem.put("rank", rank++);
            rankItem.put("name", item.getString("name"));
            rankItem.put("value", item.getInteger("value"));
            rankList.add(rankItem);
        }

        // 构建词云数据（使用所有标签）
        List<JSONObject> wordCloudData = new ArrayList<>();
        for (JSONObject item : labelStats) {
            JSONObject wordCloudItem = new JSONObject();
            wordCloudItem.put("name", item.getString("name"));
            wordCloudItem.put("value", item.getInteger("value"));
            wordCloudData.add(wordCloudItem);
        }

        resultMap.put("topRanks", rankList);
        resultMap.put("wordCloud", wordCloudData);

        return resultMap;
    }

    /**
     * 根据城市名称获取下辖区县列表
     *
     * @param cityName 城市名称
     * @return 区县列表
     */
    private List<String> getDistrictsByCity(String cityName) {
        // 模拟数据，实际应从数据库获取
        Map<String, List<String>> cityDistrictsMap = new HashMap<>();

        cityDistrictsMap.put("杭州市", Arrays.asList("上城区", "下城区", "江干区", "拱墅区", "西湖区", "滨江区", "萧山区", "余杭区", "富阳区", "临安区"));
        cityDistrictsMap.put("宁波市", Arrays.asList("海曙区", "江北区", "北仑区", "镇海区", "鄞州区", "奉化区", "余姚市", "慈溪市", "象山县", "宁海县"));
        cityDistrictsMap.put("温州市", Arrays.asList("鹿城区", "龙湾区", "瓯海区", "洞头区", "永嘉县", "平阳县", "苍南县", "文成县", "泰顺县", "瑞安市", "乐清市"));
        cityDistrictsMap.put("绍兴市", Arrays.asList("越城区", "柯桥区", "上虞区", "新昌县", "诸暨市", "嵊州市"));
        cityDistrictsMap.put("湖州市", Arrays.asList("吴兴区", "南浔区", "德清县", "长兴县", "安吉县"));
        cityDistrictsMap.put("嘉兴市", Arrays.asList("南湖区", "秀洲区", "嘉善县", "海盐县", "海宁市", "平湖市", "桐乡市"));
        cityDistrictsMap.put("金华市", Arrays.asList("婺城区", "金东区", "武义县", "浦江县", "磐安县", "兰溪市", "义乌市", "东阳市", "永康市"));
        cityDistrictsMap.put("衢州市", Arrays.asList("柯城区", "衢江区", "常山县", "开化县", "龙游县", "江山市"));
        cityDistrictsMap.put("舟山市", Arrays.asList("定海区", "普陀区", "岱山县", "嵊泗县"));
        cityDistrictsMap.put("台州市", Arrays.asList("椒江区", "黄岩区", "路桥区", "三门县", "天台县", "仙居县", "温岭市", "临海市", "玉环市"));
        cityDistrictsMap.put("丽水市", Arrays.asList("莲都区", "青田县", "缙云县", "遂昌县", "松阳县", "云和县", "庆元县", "景宁畲族自治县", "龙泉市"));

        // 如果没有该城市的数据，返回空列表
        return cityDistrictsMap.getOrDefault(cityName, new ArrayList<>());
    }

    /**
     * 设置法律资源标签
     * 为每个资源随机选择父标签和关联的子标签，并存储到law_education_resources_label_contact表中
     * 处理逻辑：
     * 1. 获取所有有效标签（状态为1）
     * 2. 构建标签父子关系映射
     * 3. 获取所有有效资源（状态为1,2,5,6）
     * 4. 对每个资源随机选择一个父标签和其关联的子标签
     * 5. 将关系插入到law_education_resources_label_contact表中，保存父标签关系
     */
    @Override
    public void lawResourceTag() {
        log.info("开始处理法律资源标签设置");
        try {
            // 获取所有有效标签
            List<LerLabel> lerLabels = lerLabelService.list(new QueryWrapper<LerLabel>().lambda()
                    .eq(LerLabel::getStatus, 1));
            if (CollUtil.isEmpty(lerLabels)) {
                log.warn("未找到有效的标签数据");
                return;
            }

            //寻找子标签
            Set<String> childIds = lerLabels.stream().filter(label -> StringUtils.isNotBlank(label.getPid()))
                    .map(LerLabel::getId).collect(Collectors.toSet());
            int size = childIds.size();


            // 获取所有有效资源
            List<LeResources> leResources = leResourcesService.list(new QueryWrapper<LeResources>().lambda()
                    .in(LeResources::getStatus, 1, 2, 5, 6));
            if (CollUtil.isEmpty(leResources)) {
                log.warn("未找到需要处理的资源数据");
                return;
            }

            log.info("共找到{}个资源需要处理标签", leResources.size());
            for (LeResources leResource : leResources) {
                //随机选择一个子标签
                String childId = childIds.stream().skip(RandomUtil.randomInt(size)).findFirst().orElse(null);
                //构建父子关系
                String parentId = lerLabels.stream().filter(label -> label.getId().equals(childId))
                        .map(LerLabel::getPid).findFirst().orElse(null);
                lerLabelMapper.insertLabelContact(IdUtil.fastSimpleUUID(), leResource.getId(), childId, parentId);
            }


        } catch (Exception e) {
            log.error("处理法律资源标签时发生异常", e);
            // 不抛出异常，只记录日志
        }
    }

    @Override
    public Map<String, Object> lawCultureMap(String areaName, Integer type, Integer positionLevel) {
        Map<String, Object> resultMap = new HashMap<>();
        // 根据类型和位置等级获取对应的资源
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(LawPosition::getPositionType,
                LawPositionBaseEnum.TRADITIONAL_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.RED_LEGAL_CULTURE.getCode(),
                LawPositionBaseEnum.CONSTITUTION_EDUCATION.getCode(),
                LawPositionBaseEnum.CIVIL_CODE_EDUCATION.getCode(),
                LawPositionBaseEnum.YOUTH_LEGAL_EDUCATION.getCode(),
                LawPositionBaseEnum.OTHER_LOCAL_LEGAL_CULTURE.getCode());
        if (ObjectUtil.isNotEmpty(type)) {
            queryWrapper.lambda().eq(LawPosition::getPositionType, type);
        }
        if (ObjectUtil.isNotEmpty(positionLevel)) {
            queryWrapper.lambda().eq(LawPosition::getPositionLevel, positionLevel);
        }
        if (ObjectUtil.isNotEmpty(areaName)) {
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                queryWrapper.lambda().eq(LawPosition::getCity, areaName);
            } else {
                queryWrapper.lambda().eq(LawPosition::getArea, areaName);
            }
        }
        List<LawPosition> positionList = lawPositionService.list(queryWrapper);

        // 将资源按区域分组显示数量,省级则看各地市分布，地市看区县
        if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            // 省级资源分布
            Map<String, Integer> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getCity, Collectors.counting()))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().intValue()));
            resultMap.put("amountMap", resourcesByArea);
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 地市资源分布
            Map<String, Integer> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getArea, Collectors.counting()))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().intValue()));
            resultMap.put("amountMap", resourcesByArea);
        } else {
            // 区县资源分布
            Map<String, Integer> resourcesByArea = positionList.stream()
                    .collect(Collectors.groupingBy(LawPosition::getArea, Collectors.counting()))
                    .entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().intValue()));
            resultMap.put("amountMap", resourcesByArea);
        }

        resultMap.put("list", positionList);


        return resultMap;
    }

    @Override
    public List<JSONObject> lawCultivateAnalyseArea(String areaName, Integer type) {
        Map<String, Object> map = new HashMap<>();

        //普法干部地市区县分布
        if (type == 1) {
            //省级
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                List<JSONObject> jsonObjectList = bigScreenMapper.lawCultivateAnalyseProvince();
                //按照地市顺序排
                List<JSONObject> result = new ArrayList<>();
                for (String city : CommonConstant.CITY_LIST) {
                    for (JSONObject jsonObject : jsonObjectList) {
                        if (city.equals(jsonObject.getString("city"))) {
                            result.add(jsonObject);
                            break;
                        }
                    }
                }
                return result;
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市

                return bigScreenMapper.lawCultivateAnalyseCity(areaName);
            } else {
                //区县
                return bigScreenMapper.lawCultivateAnalyseArea(areaName);
            }
        }

        //法律明白人地市分布
        if (type == 2) {
            //省级
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                return bigScreenMapper.lawUnderstandProvince();
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市
                return bigScreenMapper.lawUnderstandCity(areaName);
            } else {
                //区县
                return bigScreenMapper.lawUnderstandArea(areaName);
            }
        }


        return null;
    }

    @Override
    public Map<String, Object> lawVillage(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                //省级民主法治示范村数量
                jsonObject.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getCity, city).eq(LawPosition::getPositionLevel, 2)));
                //国家级民主法治示范村数量
                jsonObject.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getCity, city).eq(LawPosition::getPositionLevel, 1)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
            return map;
        } else {
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    //省级民主法治示范村数量
                    jsonObject.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getArea, area).eq(LawPosition::getPositionLevel, 2)));
                    //国家级民主法治示范村数量
                    jsonObject.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, "1", "4").eq(LawPosition::getArea, area).eq(LawPosition::getPositionLevel, 1)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
                return map;
            }
        }
        return null;
    }

    @Override
    public Map<String, Object> lawVillageTotal(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            //全国级数量
            map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getPositionLevel, 1)));
            //省级数量
            map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getPositionLevel, 2)));
            //各地市数量
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, city)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
        } else {
            //地市
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                //国家级数量
                map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, areaName).eq(LawPosition::getPositionLevel, 1)));
                //省级数量
                map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getCity, areaName).eq(LawPosition::getPositionLevel, 2)));
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    jsonObject.put("value", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, area)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
            } else {
                //区县
                map.put("country", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName).eq(LawPosition::getPositionLevel, 1)));
                map.put("province", lawPositionService.count(new QueryWrapper<LawPosition>().lambda().in(LawPosition::getPositionType, 1, 4).eq(LawPosition::getArea, areaName).eq(LawPosition::getPositionLevel, 2)));
                map.put("list", new ArrayList<>());

            }

        }

        return map;
    }

    @Override
    public Map<String, Object> lawObservatory(String areaName) {
        Map<String, Object> map = new HashMap<>();
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            //省级数量
            map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
            //市级数量
            map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
            //区级数量
            map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
            //各地市数量
            List<JSONObject> jsonObjectList = new ArrayList<>();
            for (String city : CommonConstant.CITY_LIST) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("name", city);
                jsonObject.put("value", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, city).eq(ObPointInfo::getStatus, 4)));
                jsonObjectList.add(jsonObject);
            }
            map.put("list", jsonObjectList);
            return map;

        } else {
            //地市查区县的
            if (CommonConstant.CITY_LIST.contains(areaName)) {
                //地市下的区县，全国和省级各有多少
                //省级数量
                map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
                //市级数量
                map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
                //区级数量
                map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getCity, areaName).eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
                List<JSONObject> jsonObjectList = new ArrayList<>();
                List<String> childAreaNameList = cityAreaInfoMapper.getChildAreaNameList(areaName);
                for (String area : childAreaNameList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("name", area);
                    jsonObject.put("value", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, area).eq(ObPointInfo::getStatus, 4)));
                    jsonObjectList.add(jsonObject);
                }
                map.put("list", jsonObjectList);
                return map;
            } else {
                //区县查自己的
                map.put("province", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 1).eq(ObPointInfo::getStatus, 4)));
                map.put("city", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 2).eq(ObPointInfo::getStatus, 4)));
                map.put("area", obPointInfoService.count(new QueryWrapper<ObPointInfo>().lambda().eq(ObPointInfo::getArea, areaName).eq(ObPointInfo::getPointLevel, 3).eq(ObPointInfo::getStatus, 4)));
                map.put("list", new ArrayList<>());
                return map;
            }
        }
    }

    @Override
    public Map<String, Object> lawCultivateAnalyse(String areaName) {
        Map<String, Object> resultMap = new HashMap<>();
        String areaCode = getAreaCodeByName(areaName);

        //普法干部
        if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            List<ScreenVo> femaleAndMale = bigScreenMapper.lawCultivateSex(null);
            int total = 0;
            for (ScreenVo screenVo : femaleAndMale) {
                if (screenVo.getCode() == 1) {
                    screenVo.setName("男");
                }
                if (screenVo.getCode() == 2) {
                    screenVo.setName("女");
                }
                total += screenVo.getValue();
            }
            //普法干部总人数
            resultMap.put("pfgbTotal", total);
        } else {
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                //男女比例
                List<ScreenVo> femaleAndMale = bigScreenMapper.lawCultivateSex(areaCode);
                int total = 0;
                for (ScreenVo screenVo : femaleAndMale) {
                    if (screenVo.getCode() == 1) {
                        screenVo.setName("男");
                    }
                    if (screenVo.getCode() == 2) {
                        screenVo.setName("女");
                    }
                    total += screenVo.getValue();
                }
                resultMap.put("pfgbTotal", total);
            } else {
                resultMap.put("pfgbTotal", 0);
            }
        }

        //法律明白人
        if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
            int lawLearnerTotal = lawLearnerTotal(CommonConstant.TOP_AREA_NAME);
            resultMap.put("flmbrTotal", lawLearnerTotal);
        } else {
            String orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                int lawLearnerTotal = lawLearnerTotal(areaName);
                resultMap.put("flmbrTotal", lawLearnerTotal);
            } else {
                resultMap.put("flmbrTotal", 0);
            }
        }

        //普法志愿者
        //todo 普法志愿者人数统计
        resultMap.put("pfzyzTotal", 0);

        return resultMap;
    }

    @Override
    public PageResult<?> lawCultivateAnalysePage(String areaName, Integer type) {
        String areaCode = getAreaCodeByName(areaName);

        // 根据类型获取不同的数据
        if (type == null) {
            type = 1; // 默认为普法干部
        }

        // 创建分页对象
        Page<LawUserVo> page = PageFactory.defaultPage();

        // 1-普法干部 2-法律明白人 3-普法志愿者
        switch (type) {
            case 1:
                // 普法干部数据 - 使用数据库分页
                if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                    page = bigScreenMapper.lawCultivateUserPageWithPagination(page, null);
                } else {
                    String orgId = getOrgIdByName(areaName);
                    if (ObjectUtil.isNotEmpty(orgId)) {
                        page = bigScreenMapper.lawCultivateUserPageWithPagination(page, areaCode);
                    }
                }
                break;
            case 2:
                // 法律明白人数据 - 使用数据库分页
                if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                    page = bigScreenMapper.lawLearnerUserPageWithPagination(page, null);
                } else {
                    String orgId = getOrgIdByName(areaName);
                    if (ObjectUtil.isNotEmpty(orgId)) {
                        page = bigScreenMapper.lawLearnerUserPageWithPagination(page, areaCode);
                    }
                }
                break;
            case 3:
                // 普法志愿者数据
                // TODO: 实现普法志愿者数据查询
                break;
            default:
                break;
        }

        // 创建分页结果对象
        PageResult<LawUserVo> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(page.getRecords());
        return result;
    }

    @Override
    public PageResult<JSONObject> lawCultivateAnalyseAreaPage(String areaName, Integer type) {
        List<JSONObject> dataList = new ArrayList<>();

        // 1-普法干部 2-法律明白人 3-普法志愿者
        if (type == 1) {
            // 普法干部地市区县分布
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                List<JSONObject> jsonObjectList = bigScreenMapper.lawCultivateAnalyseProvince();
                // 按照地市顺序排
                for (String city : CommonConstant.CITY_LIST) {
                    for (JSONObject jsonObject : jsonObjectList) {
                        if (city.equals(jsonObject.getString("city"))) {
                            dataList.add(jsonObject);
                            break;
                        }
                    }
                }
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                // 地市
                dataList = bigScreenMapper.lawCultivateAnalyseCity(areaName);
            } else {
                // 区县
                dataList = bigScreenMapper.lawCultivateAnalyseArea(areaName);
            }
        } else if (type == 2) {
            // 法律明白人地市分布
            if (areaName.equals(CommonConstant.TOP_AREA_NAME)) {
                dataList = bigScreenMapper.lawUnderstandProvince();
            } else if (CommonConstant.CITY_LIST.contains(areaName)) {
                // 地市
                dataList = bigScreenMapper.lawUnderstandCity(areaName);
            } else {
                // 区县
                dataList = bigScreenMapper.lawUnderstandArea(areaName);
            }
        }
        // 使用PageUtil工具类进行分页处理
        return com.concise.common.util.PageUtil.getPageResult(dataList);
    }

    @Override
    public PageResult<JSONObject> lawVillagePage(String areaName) {
        // 使用分页查询，不再遍历每个地市
        Page<LawPosition> page = PageFactory.defaultPage();
        QueryWrapper<LawPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(LawPosition::getPositionType, "1", "4");

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 省级查询，不需要额外条件
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            // 地市级查询
            queryWrapper.lambda().eq(LawPosition::getCity, areaName);
        } else {
            // 区县级查询
            queryWrapper.lambda().eq(LawPosition::getArea, areaName);
        }

        // 执行分页查询
        Page<LawPosition> positionPage = lawPositionService.page(page, queryWrapper);

        // 转换为JSONObject列表
        List<JSONObject> resultList = new ArrayList<>();
        for (LawPosition position : positionPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", position.getId());
            jsonObject.put("name", position.getPositionName());
            jsonObject.put("city", position.getCity());
            jsonObject.put("area", position.getArea());
            jsonObject.put("addressDetail", position.getAddressDetail());
            jsonObject.put("positionLevel", position.getPositionLevel());
            jsonObject.put("levelName", "1".equals(position.getPositionLevel()) ? "国家级" : "省级");
            resultList.add(jsonObject);
        }

        // 使用正确的构造函数创建PageResult，明确指定泛型类型
        return new PageResult<JSONObject>((Page<JSONObject>) (Page<?>) positionPage, resultList);
    }

    @Override
    public PageResult<JSONObject> lawVillageTotalPage(String areaName, Integer level) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawVillageTotalPageWithPagination(page, areaName, level);
        List<JSONObject> records = page.getRecords();

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<ObPointInfo> lawObservatoryPage(String areaName, Integer level) {
        Page<ObPointInfo> page = PageFactory.defaultPage();
        QueryWrapper<ObPointInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ObPointInfo::getPointLevel, level);
        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            page = obPointInfoService.page(page, queryWrapper);
        } else if (CommonConstant.CITY_LIST.contains(areaName)) {
            queryWrapper.lambda().like(ObPointInfo::getAddressId, areaName);
            page = obPointInfoService.page(page, queryWrapper);
        } else {
            queryWrapper.lambda().like(ObPointInfo::getAddressId, areaName);
            page = obPointInfoService.page(page, queryWrapper);
        }

        return new PageResult<ObPointInfo>(page);
    }

    @Override
    public PageResult<JSONObject> lawCultivateAnalyseAreaUserPage(String areaName, Integer type) {
        String areaCode = getAreaCodeByName(areaName);

        // 创建分页对象
        Page<LawUserVo> page = PageFactory.defaultPage();

        // 根据区域和类型获取对应的人员列表
        // 1-普法干部 2-法律明白人 3-普法志愿者
        if (type == 1) {
            // 获取普法干部人员列表 - 使用数据库分页
            page = bigScreenMapper.lawCultivateUserPageWithPagination(page, areaCode);
        } else if (type == 2) {
            // 获取法律明白人人员列表 - 使用数据库分页
            page = bigScreenMapper.lawLearnerUserPageWithPagination(page, areaCode);
        } else if (type == 3) {
            // TODO: 普法志愿者数据查询
        }

        // 将LawUserVo转换为JSONObject
        List<JSONObject> jsonDataList = new ArrayList<>();
        for (LawUserVo user : page.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", user.getId());
            jsonObject.put("name", user.getName());

            // 正确处理sex字段
            String sexStr = "未知";
            if (user.getSex() != null) {
                if (user.getSex() instanceof Integer) {
                    Integer sexInt = (Integer) user.getSex();
                    sexStr = sexInt == 1 ? "男" : (sexInt == 2 ? "女" : "未知");
                } else if (user.getSex() instanceof String) {
                    String sexVal = (String) user.getSex();
                    sexStr = "1".equals(sexVal) ? "男" : ("2".equals(sexVal) ? "女" : sexVal);
                }
            }
            jsonObject.put("sex", sexStr);

            jsonObject.put("phone", user.getPhone());
            jsonObject.put("orgName", user.getOrgName());
            jsonDataList.add(jsonObject);
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(jsonDataList);
        return result;
    }

    @Override
    public PageResult<BenchmarkManage> citizenLegalLiteracyPage(String areaName, Integer type) {

        QueryWrapper<BenchmarkManage> queryWrapper = new QueryWrapper<>();

        // 根据版本类型筛选
        if (ObjectUtil.isNotEmpty(type)) {
            queryWrapper.lambda().eq(BenchmarkManage::getVersionType, type);
        }

        // 根据区域名称筛选
        if (StringUtils.isNotBlank(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            String areaCode = getAreaCodeByName(areaName);
            if (StringUtils.isNotBlank(areaCode)) {
                queryWrapper.lambda().eq(BenchmarkManage::getAreaCode, areaCode);
            }

            if (CommonConstant.CITY_LIST.contains(areaName)) {
                queryWrapper.lambda().eq(BenchmarkManage::getCity, areaName);
            } else {
                queryWrapper.lambda().eq(BenchmarkManage::getArea, areaName);
            }
        }

        // 只查询未删除的记录
//        queryWrapper.lambda().eq(BenchmarkManage::getDelFlag, 0);

        // 按创建时间降序排序
        queryWrapper.lambda().orderByDesc(BenchmarkManage::getCreateTime);

        // 执行分页查询
        Page<BenchmarkManage> page = PageFactory.defaultPage();
        Page<BenchmarkManage> resultPage = benchmarkManageService.page(page, queryWrapper);

        return new PageResult<>(resultPage);
    }

    @Override
    public PageResult<JSONObject> lawNewsPage(String areaName, String labelName) {
        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 查询标签
        QueryWrapper<LerLabel> labelQuery = new QueryWrapper<>();
        labelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签

        // 如果指定了标签名称，进行过滤
        if (StringUtils.isNotBlank(labelName)) {
            labelQuery.lambda().like(LerLabel::getName, labelName);
        }

        List<LerLabel> labels = lerLabelService.list(labelQuery);

        // 如果没有找到标签，返回空结果
        if (CollectionUtil.isEmpty(labels)) {
            return com.concise.common.util.PageUtil.getPageResult(new ArrayList<JSONObject>());
        }

        // 收集所有标签ID
        List<String> labelIds = labels.stream().map(LerLabel::getId).collect(Collectors.toList());

        // 收集所有资源ID
        List<String> resourceIds = new ArrayList<>();
        for (String labelId : labelIds) {
            List<String> ids = ((LerLabelMapper) lerLabelService.getBaseMapper()).selectResContact(labelId);
            if (CollectionUtil.isNotEmpty(ids)) {
                resourceIds.addAll(ids);
            }
        }

        // 如果没有找到资源，返回空结果
        if (CollectionUtil.isEmpty(resourceIds)) {
            return com.concise.common.util.PageUtil.getPageResult(new ArrayList<JSONObject>());
        }

        // 查询资源详情
        QueryWrapper<LeResources> resourceQuery = new QueryWrapper<>();
        resourceQuery.lambda()
                .in(LeResources::getId, resourceIds)
                .in(LeResources::getStatus, 1, 2, 5, 6); // 只查询有效资源

        // 按区域过滤
        if (ObjectUtil.isNotEmpty(deptIds)) {
            resourceQuery.lambda().in(LeResources::getDeptId, deptIds);
        }

        // 按创建时间降序排序
        resourceQuery.lambda().orderByDesc(LeResources::getCreateTime);

        // 执行分页查询
        Page<LeResources> page = PageFactory.defaultPage();
        Page<LeResources> resultPage = leResourcesService.page(page, resourceQuery);

        // 转换为JSONObject列表
        List<JSONObject> resultList = new ArrayList<>();
        for (LeResources resource : resultPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", resource.getId());
            jsonObject.put("title", resource.getTitle());  // 资讯标题
            jsonObject.put("deptName", resource.getDeptName());  // 单位
            jsonObject.put("createTime", DateUtil.format(resource.getCreateTime(), DatePattern.NORM_DATETIME_FORMAT));  // 提交时间

            // 获取资源关联的标签
            List<String> firstLevelLabels = new ArrayList<>();
            List<String> secondLevelLabels = new ArrayList<>();

            if (StringUtils.isNotBlank(resource.getLabelIds())) {
                List<String> resourceLabelIds = Arrays.asList(resource.getLabelIds().split(","));

                for (String id : resourceLabelIds) {
                    for (LerLabel label : labels) {
                        if (id.equals(label.getId())) {
                            // 判断是一级标签还是二级标签
                            if (StringUtils.isBlank(label.getPid())) {
                                // 没有父ID的是一级标签
                                firstLevelLabels.add(label.getName());
                            } else {
                                // 有父ID的是二级标签
                                secondLevelLabels.add(label.getName());
                            }
                            break;
                        }
                    }
                }
            }

            jsonObject.put("firstLevelLabels", firstLevelLabels);  // 一级标签
            jsonObject.put("secondLevelLabels", secondLevelLabels);  // 二级标签

            resultList.add(jsonObject);
        }
        Page<JSONObject> pageResult = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        pageResult.setRecords(resultList);
        // 使用PageUtil工具类创建PageResult
        return new PageResult<>(pageResult);
    }

    @Override
    public PageResult<JSONObject> lawReportPage(String areaName) {
        // 获取基础数据
        PageResult<JSONObject> pageResult = leResourcesService.lawReportPage(areaName);
        List<JSONObject> resultList = new ArrayList<>();

        // 查询所有有效标签
        QueryWrapper<LerLabel> labelQuery = new QueryWrapper<>();
        labelQuery.lambda().eq(LerLabel::getStatus, 1); // 已审核的标签
        List<LerLabel> labels = lerLabelService.list(labelQuery);

        // 处理每条记录，添加一级标签和二级标签
        for (JSONObject record : pageResult.getRows()) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", record.getString("id"));
            jsonObject.put("title", record.getString("title"));  // 资讯标题
            jsonObject.put("deptName", record.getString("dept_name"));  // 单位
            jsonObject.put("createTime", record.getDate("create_time"));  // 提交时间

            // 获取资源关联的标签
            List<String> firstLevelLabels = new ArrayList<>();
            List<String> secondLevelLabels = new ArrayList<>();

            String labelIds = record.getString("label_ids");
            if (StringUtils.isNotBlank(labelIds)) {
                List<String> resourceLabelIds = Arrays.asList(labelIds.split(","));

                for (String id : resourceLabelIds) {
                    for (LerLabel label : labels) {
                        if (id.equals(label.getId())) {
                            // 判断是一级标签还是二级标签
                            if (StringUtils.isBlank(label.getPid())) {
                                // 没有父ID的是一级标签
                                firstLevelLabels.add(label.getName());
                            } else {
                                // 有父ID的是二级标签
                                secondLevelLabels.add(label.getName());
                            }
                            break;
                        }
                    }
                }
            }

            jsonObject.put("firstLevelLabels", firstLevelLabels);  // 一级标签
            jsonObject.put("secondLevelLabels", secondLevelLabels);  // 二级标签

            resultList.add(jsonObject);
        }

        // 创建新的PageResult
        PageResult<JSONObject> result = new PageResult<>();
        result.setRows(resultList);
        result.setTotalRows(pageResult.getTotalRows());
        result.setPageNo(pageResult.getPageNo());
        result.setPageSize(pageResult.getPageSize());

        return result;
    }

    @Override
    public PageResult<JSONObject> lawTestPage(String areaName, Integer type) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        if (CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            // 全省统计，使用原来的查询方式
            page = bigScreenMapper.lawTestPageWithPagination(page, type, null);
        } else {
            // 获取区域编码
            String areaCode = getAreaCodeByName(areaName);

            if (ObjectUtil.isNotEmpty(areaCode)) {
                // 获取包含本级和下级的所有区域代码
                Set<String> areaCodes = cityAreaInfoService.getAreaCodes(areaCode);

                if (CollectionUtil.isNotEmpty(areaCodes)) {
                    // 对于 exam_result 表，需要先获取区域名称
                    List<String> areaNamesList = cityAreaInfoMapper.getAreaNamesByIds(areaCodes);
                    Set<String> areaNames = CollectionUtil.isNotEmpty(areaNamesList) ? new HashSet<>(areaNamesList) : Collections.emptySet();

                    // 使用新的范围查询方法
                    page = bigScreenMapper.lawTestPageWithPaginationByAreaCodes(page, type, areaCodes, areaNames);
                } else {
                    // 如果没有找到下级区域，使用原来的模糊查询方式
                    page = bigScreenMapper.lawTestPageWithPagination(page, type, areaCode);
                }
            } else {
                // 如果没有找到区域编码，使用原来的查询方式
                page = bigScreenMapper.lawTestPageWithPagination(page, type, null);
            }
        }

        List<JSONObject> records = page.getRecords();

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<JSONObject> lawCulturePage(String areaName, Integer type, Integer positionLevel) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawCulturePageWithPagination(page, type, positionLevel, areaName);
        List<JSONObject> records = page.getRecords();

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<JSONObject> lawArtPage(String areaName, Integer type) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawArtPage(page, String.valueOf(type), areaName);
        List<JSONObject> records = page.getRecords();

        // 处理每条记录的标签
        for (JSONObject record : records) {
            String labelIds = record.getString("label_ids");
            if (StrUtil.isNotEmpty(labelIds)) {
                List<String> labelNames = getLabelNamesByIds(labelIds);
                record.put("labelNames", labelNames);
            }

            // 确保所有必要字段存在
            if (!record.containsKey("theme_type") || record.getString("theme_type") == null) {
                record.put("theme_type", "");
            }

            if (!record.containsKey("audience_type") || record.getString("audience_type") == null) {
                record.put("audience_type", "");
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<JSONObject> lawArtCategoryPage(String areaName, String labelName) {
        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawArtCategoryPage(page, labelName, areaName);
        List<JSONObject> records = page.getRecords();

        // 处理每条记录的标签
        for (JSONObject record : records) {
            String labelIds = record.getString("label_ids");
            if (StrUtil.isNotEmpty(labelIds)) {
                List<String> labelNames = getLabelNamesByIds(labelIds);
                record.put("labelNames", labelNames);
            }

            // 确保所有必要字段存在
            if (!record.containsKey("theme_type") || record.getString("theme_type") == null) {
                record.put("theme_type", "");
            }

            if (!record.containsKey("audience_type") || record.getString("audience_type") == null) {
                record.put("audience_type", "");
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    @Override
    public PageResult<JSONObject> lawLearnPage(String areaName) {
        // 获取区域代码和组织机构ID
        String areaCode = null;
        String orgId = null;
        Set<String> deptIds = null;

        if (ObjectUtil.isNotEmpty(areaName) && !CommonConstant.TOP_AREA_NAME.equals(areaName)) {
            areaCode = getAreaCodeByName(areaName);
            orgId = getOrgIdByName(areaName);
            if (ObjectUtil.isNotEmpty(orgId)) {
                deptIds = sysOrgService.getDeptIds(orgId);
            }
        }

        // 查询资源浏览记录
        QueryWrapper<LawEducationResourcesView> viewQueryWrapper = new QueryWrapper<>();

        // 按区域过滤
        if (ObjectUtil.isNotEmpty(deptIds)) {
            // 如果有特定区域，则需要先获取该区域的资源ID列表
            QueryWrapper<LeResources> resourceQueryWrapper = new QueryWrapper<>();
            resourceQueryWrapper.lambda().in(LeResources::getDeptId, deptIds);
            List<String> areaResourceIds = leResourcesService.list(resourceQueryWrapper)
                    .stream().map(LeResources::getId).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(areaResourceIds)) {
                viewQueryWrapper.lambda().in(LawEducationResourcesView::getResourceId, areaResourceIds);
            } else {
                // 如果没有找到资源，返回空结果
                return new PageResult<>(new Page<>());
            }
        }

        // 按浏览时间降序排序
        viewQueryWrapper.lambda().orderByDesc(LawEducationResourcesView::getCreateTime);

        // 执行分页查询
        Page<LawEducationResourcesView> page = PageFactory.defaultPage();
        Page<LawEducationResourcesView> viewPage = lawEducationResourcesViewService.page(page, viewQueryWrapper);

        // 处理查询结果
        List<JSONObject> resultList = new ArrayList<>();
        for (LawEducationResourcesView view : viewPage.getRecords()) {
            JSONObject jsonObject = new JSONObject();

            // 获取资源信息
            LeResources resource = leResourcesService.getById(view.getResourceId());
            if (resource != null) {
                jsonObject.put("resourceId", resource.getId());
                jsonObject.put("resourceName", resource.getTitle());
                jsonObject.put("resourceType", resource.getType());
                jsonObject.put("deptName", resource.getDeptName());  // 共享单位

                // 资源类型名称
                String typeName = "其他";
                if (resource.getType() != null) {
                    if (resource.getType().startsWith("1") || resource.getType().startsWith("2")) {
                        typeName = "普法文章";
                    } else if (resource.getType().startsWith("3") || resource.getType().startsWith("4")) {
                        typeName = "普法视频";
                    }
                }
                jsonObject.put("typeName", typeName);

                // 添加资源级别信息
                String resourceLevel = "省级";
                if (ObjectUtil.isNotEmpty(resource.getSource())) {
                    if ("0".equals(resource.getSource())) {
                        resourceLevel = "省级";
                    } else if ("1".equals(resource.getSource())) {
                        resourceLevel = "市级";
                    } else if ("2".equals(resource.getSource())) {
                        resourceLevel = "区县级";
                    }
                }
                jsonObject.put("resourceLevel", resourceLevel);

                // 添加管辖单位信息
                jsonObject.put("manageDept", resource.getDeptName());

                // 添加地点信息（如果有）
                String location = "";
                // 尝试从deptName获取地点信息
                if (StringUtils.isNotBlank(resource.getDeptName())) {
                    location = resource.getDeptName();
                }
                jsonObject.put("location", location);
            } else {
                jsonObject.put("resourceName", "未知资源");
                jsonObject.put("deptName", "");
                jsonObject.put("typeName", "");
                jsonObject.put("resourceLevel", "");
                jsonObject.put("manageDept", "");
                jsonObject.put("location", "");
            }

            // 用户信息
            SysUser user = sysUserService.getById(view.getUserId());
            if (user != null) {
                jsonObject.put("userId", user.getId());
                jsonObject.put("userName", user.getName());
                jsonObject.put("userPhone", user.getPhone());

                // 获取用户所属组织信息
                String userOrgName = "";
                // 通过用户ID查询组织信息
                SysOrg userOrg = sysOrgService.getById(user.getId());
                if (userOrg != null) {
                    userOrgName = userOrg.getName();
                }
                jsonObject.put("userOrg", userOrgName);

                // 添加用户级别信息
                String userLevel = "";
                if (StringUtils.isNotBlank(user.getCityCode()) && StringUtils.isBlank(user.getAreaCode())) {
                    userLevel = "市级";
                } else if (StringUtils.isNotBlank(user.getAreaCode())) {
                    userLevel = "区县级";
                } else {
                    userLevel = "省级";
                }
                jsonObject.put("userLevel", userLevel);

                // 添加用户地点信息
                String userLocation = "";
                if (StringUtils.isNotBlank(user.getCityCode())) {
                    // 通过cityCode查询城市名称
                    CityAreaInfo cityInfo = cityAreaInfoMapper.selectById(user.getCityCode());
                    if (cityInfo != null && StringUtils.isNotBlank(cityInfo.getArea())) {
                        userLocation = cityInfo.getArea();
                    }

                    if (StringUtils.isNotBlank(user.getAreaCode())) {
                        // 通过areaCode查询区县名称
                        CityAreaInfo districtInfo = cityAreaInfoMapper.selectById(user.getAreaCode());
                        if (districtInfo != null && StringUtils.isNotBlank(districtInfo.getArea())) {
                            userLocation += " " + districtInfo.getArea();
                        }
                    }
                }
                jsonObject.put("userLocation", userLocation);
            } else {
                jsonObject.put("userName", "未知用户");
                jsonObject.put("userOrg", "");
                jsonObject.put("userLevel", "");
                jsonObject.put("userLocation", "");
            }

            // 浏览/收藏信息
            jsonObject.put("viewTime", view.getCreateTime());  // 观看/点击时间
            jsonObject.put("isStar", view.isStar());  // 是否收藏

            resultList.add(jsonObject);
        }

        // 创建PageResult对象
        PageResult<JSONObject> pageResult = new PageResult<>();
        pageResult.setRows(resultList);
        pageResult.setPageNo((int) viewPage.getCurrent());
        pageResult.setPageSize((int) viewPage.getSize());
        pageResult.setTotalRows((int) viewPage.getTotal());

        return pageResult;
    }

    /**
     * 法治文化-主题宣传月统计
     *
     * @param areaName 区域名称
     * @return 主题宣传月资源统计
     */
    @Override
    public List<JSONObject> lawThemeMonth(String areaName) {
        // 查询主题宣传月标签
        LerLabelParam labelParam = new LerLabelParam();
        labelParam.setPid("1"); // 主题类标签的父ID
        labelParam.setStatus(1); // 已审核通过的标签
        List<LerLabel> themeLabels = lerLabelService.list(labelParam);

        // 查找"主题宣传月"标签
        String themeMonthLabelId = null;
        for (LerLabel label : themeLabels) {
            if (label.getName().contains("主题宣传月")) {
                themeMonthLabelId = label.getId();
                break;
            }
        }

        if (themeMonthLabelId == null) {
            return new ArrayList<>(); // 如果没有找到主题宣传月标签，返回空列表
        }

        // 按地区统计主题宣传月资源数量
        return bigScreenMapper.lawThemeMonthStatistics(themeMonthLabelId, areaName);
    }

    /**
     * 法治文化-主题宣传月资源分页
     *
     * @param areaName 区域名称
     * @return 主题宣传月资源分页数据
     */
    @Override
    public PageResult<JSONObject> lawThemeMonthPage(String areaName) {
        // 查询主题宣传月标签
        LerLabelParam labelParam = new LerLabelParam();
        labelParam.setPid("1"); // 主题类标签的父ID
        labelParam.setStatus(1); // 已审核通过的标签
        List<LerLabel> themeLabels = lerLabelService.list(labelParam);

        // 查找"主题宣传月"标签
        String themeMonthLabelId = null;
        for (LerLabel label : themeLabels) {
            if (label.getName().contains("主题宣传月")) {
                themeMonthLabelId = label.getId();
                break;
            }
        }

        if (themeMonthLabelId == null) {
            return new PageResult<>(new Page<>()); // 如果没有找到主题宣传月标签，返回空结果
        }

        // 创建分页对象
        Page<JSONObject> page = PageFactory.defaultPage();

        // 执行分页查询
        page = bigScreenMapper.lawThemeMonthPageWithPagination(page, themeMonthLabelId, areaName);
        List<JSONObject> records = page.getRecords();

        // 处理标签信息
        if (CollectionUtil.isNotEmpty(records)) {
            for (JSONObject item : records) {
                // 处理标签ID转换为标签名称
                String labelIds = item.getString("label_ids");
                if (StrUtil.isNotEmpty(labelIds)) {
                    // 查询标签信息并设置主题和面向群体
                    List<String> labelNames = getLabelNamesByIds(labelIds);
                    if (CollectionUtil.isNotEmpty(labelNames)) {
                        item.put("label_names", String.join(",", labelNames));
                    }
                }

                // 确保所有必要字段存在
                if (!item.containsKey("theme_type") || item.getString("theme_type") == null) {
                    item.put("theme_type", "");
                }

                if (!item.containsKey("audience_type") || item.getString("audience_type") == null) {
                    item.put("audience_type", "");
                }

                // 格式化日期
                if (item.containsKey("create_time")) {
                    Date createTime = item.getDate("create_time");
                    if (createTime != null) {
                        item.put("share_time", DateUtil.formatDateTime(createTime));
                    }
                }
            }
        }

        // 创建分页结果对象
        PageResult<JSONObject> result = new PageResult<>();
        result.setPageNo((int) page.getCurrent());
        result.setPageSize((int) page.getSize());
        result.setTotalRows(Convert.toInt(page.getTotal()));
        result.setTotalPage(Convert.toInt(page.getPages()));
        result.setRows(records);
        return result;
    }

    /**
     * 根据标签ID获取标签名称列表
     *
     * @param labelIds 标签ID字符串，多个ID用逗号分隔
     * @return 标签名称列表
     */
    private List<String> getLabelNamesByIds(String labelIds) {
        List<String> labelNames = new ArrayList<>();
        if (StrUtil.isNotEmpty(labelIds)) {
            String[] ids = labelIds.split(",");
            for (String id : ids) {
                LerLabel label = lerLabelService.getById(id);
                if (label != null && StrUtil.isNotEmpty(label.getName())) {
                    labelNames.add(label.getName());
                }
            }
        }
        return labelNames;
    }
}
